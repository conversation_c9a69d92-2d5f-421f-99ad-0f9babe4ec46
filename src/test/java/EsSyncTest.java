import com.sure.question.QuestionApplication;
import com.sure.question.jobHandler.XxlJobHandler;
import com.sure.question.mapper.PaperQuestionMapper;
import com.sure.question.mapper.QuestionEsSyncMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class EsSyncTest {
    @Resource
    private XxlJobHandler xxlJobHandler;
    @Resource
    private QuestionEsSyncMapper questionEsSyncMapper;
    @Resource
    private PaperQuestionMapper paperQuestionMapper;

    @Test
    public void test() {
//        List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds("1442394131214983168");
//        questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);

        System.out.println(xxlJobHandler.syncQuesV2(""));
    }
}
