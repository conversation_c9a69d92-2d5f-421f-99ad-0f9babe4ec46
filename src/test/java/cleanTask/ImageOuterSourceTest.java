package cleanTask;

import cleanTask.util.BankTaskNameUtil;
import cleanTask.util.ImageElementUtil;
import cleanTask.util.ImageUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.CleanTask;
import com.sure.question.entity.CleanTaskLog;
import com.sure.question.entity.CleanTaskQuestion;
import com.sure.question.entity.CleanTaskSmallQuestion;
import com.sure.question.enums.cleanTask.ConfirmStatusEnum;
import com.sure.question.mapper.CleanTaskLogMapper;
import com.sure.question.mapper.CleanTaskMapper;
import com.sure.question.mapper.CleanTaskQuestionMapper;
import com.sure.question.mapper.CleanTaskSmallQuestionMapper;
import com.sure.question.service.CleanTaskService;
import com.sure.question.service.OssManager;
import com.sure.question.vo.question.SmallQuestionOptionVo;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 检查题目中图片外链并处理
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class ImageOuterSourceTest {
    @Resource
    private CleanTaskService cleanTaskService;
    @Resource
    private CleanTaskMapper cleanTaskMapper;
    @Resource
    private CleanTaskQuestionMapper cleanTaskQuestionMapper;
    @Resource
    private CleanTaskSmallQuestionMapper cleanTaskSmallQuestionMapper;
    @Resource
    private CleanTaskLogMapper cleanTaskLogMapper;
    @Resource
    private OssManager ossManager;
    @Value("${aliyun.ossTkImg.bucketHosts}")
    private List<String> ImgHosts;

    private final Logger logger = LoggerFactory.getLogger("cleanTask");
    private static final String taskName = "********清理图片外链";
    private final Map<String, BiConsumer<Integer, CleanTaskService.IHandleQuestionHtml>> taskNameRunnerMap = new HashMap<>();

    @PostConstruct
    void buildTaskNameRunnerMap() {
        taskNameRunnerMap.put(BankTaskNameUtil.getPublicBankTaskName(taskName), cleanTaskService::runPublicBankTask);
        taskNameRunnerMap.put(BankTaskNameUtil.getCoachBookTaskName(taskName), cleanTaskService::runCoachBookTask);
        taskNameRunnerMap.put(BankTaskNameUtil.getCoachBookCustomTaskName(taskName), cleanTaskService::runCoachBookCustomTask);
        taskNameRunnerMap.put(BankTaskNameUtil.getPushQuestionTaskName(taskName), cleanTaskService::runPushQuestionTask);
    }

    @Test
    public void startPublicBank() {
        start(BankTaskNameUtil.getPublicBankTaskName(taskName));
    }

    @Test
    public void updateAllPublicBank() {
        updateAll(BankTaskNameUtil.getPublicBankTaskName(taskName));
    }

    @Test
    public void startCoachBook() {
        start(BankTaskNameUtil.getCoachBookTaskName(taskName));
    }

    @Test
    public void updateAllCoachBook() {
        updateAll(BankTaskNameUtil.getCoachBookTaskName(taskName));
    }

    @Test
    public void startCoachBookCustom() {
        start(BankTaskNameUtil.getCoachBookCustomTaskName(taskName));
    }

    @Test
    public void updateAllCoachBookCustom() {
        updateAll(BankTaskNameUtil.getCoachBookCustomTaskName(taskName));
    }

    @Test
    public void startPushQuestion() {
        start(BankTaskNameUtil.getPushQuestionTaskName(taskName));
    }

    @Test
    public void updateAllPushQuestion() {
        updateAll(BankTaskNameUtil.getPushQuestionTaskName(taskName));
    }

    private void start(String taskName) {
        BiConsumer<Integer, CleanTaskService.IHandleQuestionHtml> runner = taskNameRunnerMap.get(taskName);
        if (runner == null) {
            return;
        }
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>().eq(CleanTask::getName, taskName));
        for (CleanTask task : cleanTaskList) {
            runner.accept(task.getId(), this::replaceOuterSrcImages);
        }
    }

    private void updateAll(String taskName) {
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>().eq(CleanTask::getName, taskName));
        for (CleanTask task : cleanTaskList) {
            cleanTaskService.updateAllConfirmedQuestions(task.getId());
        }
    }

    /**
     * 替换一段html文本中的外链图片
     * @return 无变更返回null，否则返回变更后的html
     */
    private String replaceOuterSrcImages(String html, String questionId) throws Exception {
        if (StringUtils.isBlank(html)) {
            return null;
        }
        boolean flag = false;
        Document doc = Jsoup.parseBodyFragment(html);
        Elements imgElements = doc.getElementsByTag("img");
        for (Element img : imgElements) {
            String src = img.attr("src");
            if (isOutSrc(src)) {
                flag = true;
                String oldHtml = img.outerHtml();
                String newSrc = replaceSrc(src, questionId);
                img.attr("src", newSrc);
                ImageElementUtil.cleanAttributes(img);
                String newHtml = img.outerHtml();
                logger.info("替换图片: {}, {}", oldHtml, newHtml);
            }
        }
        if (flag) {
            return doc.body().html();
        }
        else {
            return null;
        }
    }

    private boolean isOutSrc(String src) {
        return StringUtils.isEmpty(src) || !src.startsWith(ImgHosts.get(0));
    }

    private String replaceSrc(String src, String questionId) throws Exception {
        String ossName;
        byte[] bytes;
        try {
            ossName = ImageUtil.getImageOSSName(src, questionId);
        }
        catch (Exception ex) {
            throw new Exception("获取图片扩展名失败：" + src);
        }
        try {
            bytes = ImageUtil.getImageBytes(src);
        }
        catch (Exception ex) {
            throw new Exception("获取图片数据失败：" + src);
        }
        try {
            BufferedImage img = ImageIO.read(new ByteArrayInputStream(bytes));
            if (img == null || img.getWidth() <= 0 || img.getHeight() <= 0) {
                throw new Exception("图片数据错误：" + src);
            }
        }
        catch (Exception ex) {
            throw new Exception("图片数据错误：" + src);
        }
        try {
            ossManager.uploadTK(ossName, new ByteArrayInputStream(bytes));
        }
        catch (Exception ex) {
            throw new Exception("上传图片失败：" + ossName + "\n" + ex.getMessage());
        }
        return ImgHosts.get(0) + ossName;
    }


    /**
     * 公共库先找出所有含有外链图片的题目，再统一替换
     */
    @Test
    public void findAll() {
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>().eq(CleanTask::getName, "清理图片外链"));
        for (CleanTask task : cleanTaskList) {
            cleanTaskService.runPublicBankTask(task.getId(), (html, paperId) -> this.findOutSrcImage(html));
        }
    }

    private String findOutSrcImage(String html) {
        if (StringUtils.isBlank(html)) {
            return null;
        }
        if (!html.contains("<img")) {
            return null;
        }
        boolean flag = false;
        Document doc = Jsoup.parseBodyFragment(html);
        Elements imgElements = doc.getElementsByTag("img");
        for (Element img : imgElements) {
            String src = img.attr("src");
            if (isOutSrc(src)) {
                logger.info("外链图片, {}", img.outerHtml());
                flag = true;
            }
        }
        if (flag) {
            return html;
        }
        return null;
    }

    @Test
    public void replaceAll() {
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>().eq(CleanTask::getName, "清理图片外链"));
        List<Integer> taskIds = cleanTaskList.stream().map(CleanTask::getId).collect(Collectors.toList());
        List<CleanTaskQuestion> cleanTaskQuestionList = cleanTaskQuestionMapper.selectList(new LambdaQueryWrapper<CleanTaskQuestion>()
                .in(CleanTaskQuestion::getTaskId, taskIds)
                .eq(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Init.getCode()));
        List<CleanTaskSmallQuestion> cleanTaskSmallQuestionList = cleanTaskSmallQuestionMapper.selectList(new LambdaQueryWrapper<CleanTaskSmallQuestion>()
                .in(CleanTaskSmallQuestion::getTaskId, taskIds));
        Map<String, List<CleanTaskSmallQuestion>> questionIdSmallQuestionMap = cleanTaskSmallQuestionList.stream().collect(
                Collectors.groupingBy(CleanTaskSmallQuestion::getQuestionId));
        for (CleanTaskQuestion q : cleanTaskQuestionList) {
            List<CleanTaskSmallQuestion> sqList = questionIdSmallQuestionMap.get(q.getQuestionId());
            if (sqList == null) {
                sqList = Collections.emptyList();
            }
            // 处理题目并记录错误
            try {
                handleOneQuestion(q, sqList);
            }
            catch (Exception ex) {
                String message = ex.getMessage();
                String smallQuestionId = null;
                if (StringUtils.isNotEmpty(message)) {
                    Matcher matcher = Pattern.compile("^小题[I,i]d[:：]\\s*(\\d+)[,;\\s]*").matcher(message);
                    if (matcher.find()) {
                        smallQuestionId = matcher.group(1);
                        message = message.substring(matcher.group(0).length());
                    }
                }
                CleanTaskLog errorLog = new CleanTaskLog();
                errorLog.setTaskId(q.getTaskId());
                errorLog.setPaperId(q.getPaperId());
                errorLog.setQuestionId(q.getQuestionId());
                errorLog.setSmallQuestionId(smallQuestionId);
                errorLog.setContent(message);
                errorLog.setCreateTime(new Date());
                cleanTaskLogMapper.insert(errorLog);
                continue;
            }
            // 更新内容并确认
            for (CleanTaskSmallQuestion sq : sqList) {
                cleanTaskSmallQuestionMapper.update(null, new LambdaUpdateWrapper<CleanTaskSmallQuestion>()
                        .eq(CleanTaskSmallQuestion::getTaskId, sq.getTaskId())
                        .eq(CleanTaskSmallQuestion::getQuestionId, sq.getQuestionId())
                        .eq(CleanTaskSmallQuestion::getSmallQuestionId, sq.getSmallQuestionId())
                        .set(CleanTaskSmallQuestion::getContentHtml, sq.getContentHtml())
                        .set(CleanTaskSmallQuestion::getOptions, sq.getOptions())
                        .set(CleanTaskSmallQuestion::getAnswerHtml, sq.getAnswerHtml())
                        .set(CleanTaskSmallQuestion::getExplanation, sq.getExplanation()));
            }
            cleanTaskQuestionMapper.update(null, new LambdaUpdateWrapper<CleanTaskQuestion>()
                    .eq(CleanTaskQuestion::getTaskId, q.getTaskId())
                    .eq(CleanTaskQuestion::getQuestionId, q.getQuestionId())
                    .set(CleanTaskQuestion::getContentHtml, q.getContentHtml())
                    .set(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Confirmed.getCode())
                    .set(CleanTaskQuestion::getConfirmTime, new Date()));
        }
    }

    private void handleOneQuestion(CleanTaskQuestion q, List<CleanTaskSmallQuestion> sqList) throws Exception {
        if (StringUtils.isNotEmpty(q.getOldContentHtml())) {
            q.setContentHtml(replaceHtmlKeepSame(q.getOldContentHtml(), q.getQuestionId()));
        }
        for (CleanTaskSmallQuestion sq : sqList) {
            try {
                handleOneBranch(sq);
            }
            catch (Exception ex) {
                throw new Exception("小题Id:" + sq.getSmallQuestionId() + "," + ex.getMessage());
            }
        }
    }

    private void handleOneBranch(CleanTaskSmallQuestion sq) throws Exception {
        String questionId = sq.getQuestionId();
        if (StringUtils.isNotEmpty(sq.getOldContentHtml())) {
            sq.setContentHtml(replaceHtmlKeepSame(sq.getOldContentHtml(), questionId));
        }
        if (StringUtils.isNotEmpty(sq.getOldOptions())) {
            List<SmallQuestionOptionVo> options = JSON.parseArray(sq.getOldOptions(), SmallQuestionOptionVo.class);
            for (SmallQuestionOptionVo option : options) {
                option.setContent(replaceHtmlKeepSame(option.getContent(), questionId));
            }
            sq.setOptions(JSON.toJSONString(options));
        }
        if (StringUtils.isNotEmpty(sq.getOldAnswerHtml())) {
            List<String> answerList = null;
            try {
                answerList = JSON.parseArray(sq.getOldAnswerHtml(), String.class);
            }
            catch (Exception ex) {
                // 当作数组解析失败，则为字符串
            }
            if (answerList != null) {
                for (int i = 0; i < answerList.size(); i++) {
                    answerList.set(i, replaceHtmlKeepSame(answerList.get(i), questionId));
                }
                sq.setAnswerHtml(JSON.toJSONString(answerList));
            }
            else {
                sq.setAnswerHtml(replaceHtmlKeepSame(sq.getOldAnswerHtml(), questionId));
            }
        }
        if (StringUtils.isNotEmpty(sq.getOldExplanation())) {
            sq.setExplanation(replaceHtmlKeepSame(sq.getOldExplanation(), questionId));
        }
    }

    private String replaceHtmlKeepSame(String html, String questionId) throws Exception {
        String newHtml = replaceOuterSrcImages(html, questionId);
        return newHtml == null ? html : newHtml;
    }
}
