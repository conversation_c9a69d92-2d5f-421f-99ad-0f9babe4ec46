package com.sure.question.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfig {
    /**
     * 用于上传PDF时并行调用OCR接口
     */
    @Bean(name = "ocrRequestExecutor")
    public ThreadPoolTaskExecutor ocrRequestExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("OcrRequest-");
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);
        return executor;
    }

    /**
     * 用于教辅录入将pdf转为图片
     */
    @Bean(name = "pdfToImageExecutor")
    public ThreadPoolTaskExecutor pdfToImageExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("PdfToImage-");
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);
        return executor;
    }

    /**
     * 用于教辅录入识别目录结构
     */
    @Bean(name = "tocRecognizeExecutor")
    public ThreadPoolTaskExecutor tocRecognizeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("TocRecognize-");
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);
        return executor;
    }

    /**
     * 用于教辅录入整页OCR
     */
    @Bean(name = "ingestionPageOcrExecutor")
    public ThreadPoolTaskExecutor ingestionPageOcrExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("IngestionPageOcr-");
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);
        return executor;
    }

    /**
     * 用于教辅录入划分题目
     */
    @Bean(name = "ingestionQuestionOcrExecutor")
    public ThreadPoolTaskExecutor ingestionQuestionOcrExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(2);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("IngestionQuestionOcr-");
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);
        return executor;
    }

    /**
     * 用于题目推荐知识点
     */
    @Bean(name = "questionRecommendKnowledgeExecutor")
    public ThreadPoolTaskExecutor questionRecommendKnowledgeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(50);
        executor.setMaxPoolSize(100);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("QuestionRecommendKnowledgeExecutor-");
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);
        return executor;
    }
}
