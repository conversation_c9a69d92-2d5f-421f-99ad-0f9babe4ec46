package com.sure.question.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "llm")
public class LLMConfig {
    private String apiKey;
    private List<Model> models;

    private static final BigDecimal MILLION = new BigDecimal("1000000");

    @Data
    public static class Model {
        private String name;
        private String provider;
        private List<PricingRule> pricingRules;
    }

    @Data
    public static class PricingRule {
        private String description;
        private Conditions conditions;
        private Price price;
    }

    @Data
    public static class Conditions {
        private TokenRange inputTokens;
        private TokenRange outputTokens;
    }

    @Data
    public static class TokenRange {
        private Integer min;
        private Integer max;
    }

    @Data
    public static class Price {
        private BigDecimal input;
        private BigDecimal output;
    }

    /**
     * 按名称找模型
     */
    public Model getModelByName(String modelName) {
        return models.stream().filter(m -> m.getName().equals(modelName)).findFirst().orElse(null);
    }

    /**
     * 计算费用
     */
    public BigDecimal calculateCost(Model model, int promptTokens, int completionTokens) {
        if (model.getPricingRules() == null || model.getPricingRules().isEmpty()) {
            throw new IllegalStateException("模型未定义计费方式: " + model.getName());
        }
        // 按顺序查找第一个匹配的规则
        for (PricingRule rule : model.getPricingRules()) {
            if (isMatch(rule.getConditions(), promptTokens, completionTokens)) {
                // 计算成本
                Price price = rule.getPrice();
                BigDecimal inputCost = new BigDecimal(promptTokens).multiply(price.getInput()).divide(MILLION, 8, RoundingMode.HALF_UP);
                BigDecimal outputCost = new BigDecimal(completionTokens).multiply(price.getOutput()).divide(MILLION, 8, RoundingMode.HALF_UP);
                return inputCost.add(outputCost);
            }
        }
        // 若无匹配项，说明配置有漏洞
        throw new IllegalStateException(String.format("模型 %s 对输入Token=%d，输出Token=%d 无匹配计费规则", model.getName(), promptTokens, completionTokens));
    }

    private static boolean isMatch(Conditions conditions, int promptTokens, int completionTokens) {
        // conditions为null（简单定价），则永远匹配
        if (conditions == null) {
            return true;
        }
        // 检查输入Token条件
        boolean inputMatch = (conditions.getInputTokens() == null) || checkRange(promptTokens, conditions.getInputTokens());
        // 检查输出Token条件
        boolean outputMatch = (conditions.getOutputTokens() == null) || checkRange(completionTokens, conditions.getOutputTokens());

        return inputMatch && outputMatch;
    }

    private static boolean checkRange(int value, TokenRange range) {
        boolean minOk = (range.getMin() == null) || (value >= range.getMin());
        boolean maxOk = (range.getMax() == null) || (value <= range.getMax());
        return minOk && maxOk;
    }
}
