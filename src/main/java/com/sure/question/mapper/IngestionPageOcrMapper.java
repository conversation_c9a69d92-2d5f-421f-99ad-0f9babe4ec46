package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.IngestionPageOcr;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

@Mapper
public interface IngestionPageOcrMapper extends BaseMapper<IngestionPageOcr> {
    int updatePageOcrStatusToWaiting(@Param("ingestionId") int ingestionId, @Param("pageImageUrls") Collection<String> pageImageUrls);

    int updatePageOcrStatusToRunning(@Param("ingestionId") int ingestionId, @Param("pageImageUrl") String pageImageUrl);

    int updateQuestionOcrStatusToWaiting(@Param("ingestionId") int ingestionId, @Param("pageImageUrls") Collection<String> pageImageUrls);

    int updateQuestionOcrStatusToRunning(@Param("ingestionId") int ingestionId, @Param("pageImageUrl") String pageImageUrl);
}




