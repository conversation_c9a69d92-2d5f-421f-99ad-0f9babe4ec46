package com.sure.question.service;

import com.sure.question.dto.llm.RequestParam;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;

import java.util.List;

public interface LlmService {
    ChatCompletionResult chat(RequestParam requestParam);

    String chatForString(RequestParam requestParam);

    <T> T chatForObject(RequestParam requestParam, Class<T> clazz);

    <T> List<T> chatForList(RequestParam requestParam, Class<T> clazz);

    ChatCompletionRequest.ChatCompletionRequestResponseFormat buildResponseFormat(String schemaName, String jsonSchema);
}
