package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.CoachBookIngestionStructure;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.StructureVo;
import com.sure.question.vo.ingestion.TocRecognizeVo;

import java.util.List;

public interface CoachBookIngestionStructureService extends IService<CoachBookIngestionStructure> {
    /**
     * 获取结构
     */
    List<StructureVo> getStructures(int ingestionId, TokenUserVo userVo);

    /**
     * 保存结构
     */
    void saveStructures(int ingestionId, List<StructureVo> structures, TokenUserVo userVo);

    /**
     * 识别目录结构
     */
    void recognizeTOCStructure(int ingestionId, TokenUserVo userVo);

    /**
     * 获取目录识别结果
     */
    TocRecognizeVo getRecognizeTOCResult(int ingestionId, TokenUserVo userVo);
}
