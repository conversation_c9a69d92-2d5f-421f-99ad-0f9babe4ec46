package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.entity.CoachBookIngestion;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.IngestionVo;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface CoachBookIngestionService extends IService<CoachBookIngestion> {
    /**
     * 获取录入项目
     */
    IngestionVo getByCoachBookId(int coachBookId, TokenUserVo tokenUserVo);

    /**
     * 创建录入项目
     */
    IngestionVo create(int coachBookId, @NotNull String fileType, TokenUserVo userVo);

    /**
     * 分割上传文件路径
     */
    List<String> splitFilePathToPublicPath(String filePath);

    /**
     * 解析页面图片
     */
    List<PageImage> parsePageImages(String pageImages, boolean toPublicPath);

    /**
     * 获取录入项目并检查权限
     */
    CoachBookIngestion getIngestionCheckPermission(int ingestionId, TokenUserVo userVo);

    /**
     * 获取录入项目并检查权限，且未完成未删除
     */
    CoachBookIngestion getIngestionCheckNotFinishedNotDeleted(int ingestionId, TokenUserVo userVo);
}
