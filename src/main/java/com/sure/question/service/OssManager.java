package com.sure.question.service;

import com.sure.question.vo.dataVo.StrStrVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;

public interface OssManager {
    void uploadTK(String objectName, InputStream inputStream);

    void uploadTK(String objectName, byte[] buffer);

    void uploadTK(String objectName, MultipartFile multipartFile);

    void uploadTK(String objectName, String filePath);

    InputStream downloadTK(String objectName);

    Boolean checkExistsTK(String objectName);

    String getPublicPathTK(String objectName);

    String getObjectNameTK(String publicPath);

    String getObjectNameMark(String publicPath);

    void uploadMark(String objectName, byte[] buffer);

    String getPublicPathMark(String objectName);

    void downloadTKObjectsBuildZip(Collection<StrStrVo> fileNameObjectNameCollection, OutputStream outputStream) throws IOException;

}
