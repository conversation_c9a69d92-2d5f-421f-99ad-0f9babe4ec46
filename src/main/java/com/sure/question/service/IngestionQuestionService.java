package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.IngestionQuestion;
import com.sure.question.vo.TokenUserVo;

import java.util.List;

public interface IngestionQuestionService extends IService<IngestionQuestion> {
    /**
     * 获取题目
     */
    List<IngestionQuestion> getQuestions(int ingestionId, long paperId, TokenUserVo userVo);

    /**
     * 添加题目
     */
    void addQuestions(int ingestionId, long paperId, List<IngestionQuestion> questions, TokenUserVo userVo);

    /**
     * 修改题目
     */
    void changeQuestions(int ingestionId, long paperId, List<IngestionQuestion> questions, TokenUserVo userVo);

    /**
     * 删除题目
     */
    void deleteQuestions(int ingestionId, long paperId, List<Long> questionIds, TokenUserVo userVo);
}
