package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.IngestionPageOcr;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.IngestionPageOcrVo;
import com.sure.question.vo.ingestion.IngestionQuestionOcrVo;

import java.util.Collection;
import java.util.List;

public interface IngestionPageOcrService extends IService<IngestionPageOcr> {
    /**
     * 获取整页识别结果
     */
    List<IngestionPageOcrVo> getIngestionPageOcrResultList(int ingestionId, Collection<String> pageImageUrls, TokenUserVo userVo);

    /**
     * 获取题目划分识别结果
     */
    List<IngestionQuestionOcrVo> getIngestionQuestionOcrResultList(int ingestionId, Collection<String> pageImageUrls, TokenUserVo userVo);

    /**
     * 开始整页识别
     */
    void startPageOcr(int ingestionId, List<String> pageImageUrls, TokenUserVo userVo);

    /**
     * 开始题目划分识别
     */
    void startQuestionOcr(int ingestionId, List<String> pageImageUrls, TokenUserVo userVo);
}
