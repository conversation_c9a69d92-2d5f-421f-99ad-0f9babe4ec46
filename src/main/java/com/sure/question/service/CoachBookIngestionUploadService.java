package com.sure.question.service;

import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.vo.TokenUserVo;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

public interface CoachBookIngestionUploadService {
    /**
     * 修改上传文件类型
     */
    void changeFileType(int ingestionId, @NotNull String fileType, TokenUserVo tokenUserVo);

    /**
     * 上传Pdf
     */
    String uploadPdf(int ingestionId, @NotNull MultipartFile file, boolean autoConvertImage, TokenUserVo userVo);

    /**
     * 将pdf转为图片
     */
    void convertPdfToImage(int ingestionId, TokenUserVo userVo);

    /**
     * 获取上传图片批次号
     */
    String getUploadImagesBatchNumber(int ingestionId, TokenUserVo userVo);

    /**
     * 上传图片
     */
    int uploadImage(int ingestionId, String batchNumber, int index, @NotNull MultipartFile file, TokenUserVo userVo);

    /**
     * 上传图片完成
     */
    List<String> uploadImageComplete(int ingestionId, String batchNumber, TokenUserVo userVo);

    /**
     * 调整已上传图片
     */
    void changeImages(int ingestionId, @NotEmpty List<String> filePath, TokenUserVo userVo);

    /**
     * 调整页面分栏
     */
    void changePageColumns(int ingestionId, List<PageImage> pageImages, TokenUserVo userVo);

    /**
     * 删除已上传文件
     */
    void deleteUploadedFile(int ingestionId, TokenUserVo userVo);
}
