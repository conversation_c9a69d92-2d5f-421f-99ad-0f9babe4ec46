package com.sure.question.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.sure.common.exception.ApiException;
import com.sure.question.service.OssManager;
import com.sure.question.vo.dataVo.StrStrVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.*;
import java.util.Collection;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service("oosManager")
public class OssManagerImpl implements OssManager {
    @Value("${aliyun.ossTkImg.bucketName}")
    private String tkBucketName;
    @Value("${aliyun.ossTkImg.bucketHosts}")
    private List<String> tkBucketHosts;
    @Value("${aliyun.ossTkImg.endpoint}")
    private String tkBucketEndpoint;
    @Value("${aliyun.ossTkImg.accessKeyId}")
    private String tkBucketAccessKeyId;
    @Value("${aliyun.ossTkImg.accessKeySecret}")
    private String tkBucketAccessKeySecret;

    @Value("${aliyun.ossSureMark.bucketName}")
    private String markBucketName;
    @Value("${aliyun.ossSureMark.bucketHosts}")
    private List<String> markBucketHosts;
    @Value("${aliyun.ossSureMark.endpoint}")
    private String markBucketEndpoint;
    @Value("${aliyun.ossSureMark.accessKeyId}")
    private String markBucketAccessKeyId;
    @Value("${aliyun.ossSureMark.accessKeySecret}")
    private String markBucketAccessKeySecret;

    private OSS tkOssClient;
    private OSS markOssClient;

    /**
     * 创建OSS客户端实例
     */
    @PostConstruct
    private void init() {
        tkOssClient = new OSSClientBuilder().build(tkBucketEndpoint, tkBucketAccessKeyId, tkBucketAccessKeySecret);
        markOssClient = new OSSClientBuilder().build(markBucketEndpoint, markBucketAccessKeyId, markBucketAccessKeySecret);
    }

    /**
     * 上传流
     */
    private void uploadInputStream(OSS ossClient, String bucketName, String objectName, InputStream inputStream) {
        ossClient.putObject(bucketName, objectName, inputStream);
    }

    /**
     * 上传buffer
     */
    private void uploadBuffer(OSS ossClient, String bucketName, String objectName, byte[] buffer) {
        try (InputStream inputStream = new ByteArrayInputStream(buffer)) {
            uploadInputStream(ossClient, bucketName, objectName, inputStream);
        } catch (IOException ex) {
            log.error("上传Buffer失败\n", ex);
            throw new ApiException("保存文件失败");
        }
    }

    /**
     * 上传MultipartFile
     */
    private void uploadMultipartFile(OSS ossClient, String bucketName, String objectName, MultipartFile multipartFile) {
        try (InputStream inputStream = multipartFile.getInputStream()) {
            uploadInputStream(ossClient, bucketName, objectName, inputStream);
        } catch (IOException ex) {
            log.error("上传MultipartFile失败\n", ex);
            throw new ApiException("保存文件失败");
        }
    }

    /**
     * 上传本地文件
     */
    private void uploadLocalFile(OSS ossClient, String bucketName, String objectName, String filePath) {
        try (FileInputStream fileInputStream = new FileInputStream(filePath)) {
            uploadInputStream(ossClient, bucketName, objectName, fileInputStream);
        } catch (IOException ex) {
            log.error("上传本地文件失败, path = {}\n", filePath, ex);
            throw new ApiException("保存文件失败");
        }
    }

    /**
     * 下载流
     */
    private InputStream downloadInputStream(OSS ossClient, String bucketName, String objectName) {
        OSSObject ossObject = ossClient.getObject(bucketName, objectName);
        return ossObject.getObjectContent();
    }

    /**
     * 检查对象是否存在
     */
    private boolean checkObjectExists(OSS ossClient, String bucketName, String objectName) {
        return ossClient.doesObjectExist(bucketName, objectName);
    }

    /**
     * 由对象名构建公开路径
     */
    private String getPublicPath(String host, String objectName) {
        if (host.endsWith("/")) {
            if (objectName.startsWith("/")) {
                return host + objectName.substring(1);
            } else {
                return host + objectName;
            }
        } else {
            if (objectName.startsWith("/")) {
                return host + objectName;
            } else {
                return host + "/" + objectName;
            }
        }
    }

    /**
     * 从公开路径提取对象名
     */
    private String getObjectName(List<String> hosts, String publicPath) {
        if (StringUtils.isEmpty(publicPath)) {
            return "";
        }
        for (String host : hosts) {
            String prefix = host.endsWith("/") ? host : host + "/";
            if (publicPath.startsWith(prefix)) {
                return publicPath.substring(prefix.length());
            }
        }
        return "";
    }

    @Override
    public void uploadTK(String objectName, InputStream inputStream) {
        uploadInputStream(tkOssClient, tkBucketName, objectName, inputStream);
    }

    @Override
    public void uploadTK(String objectName, byte[] buffer) {
        uploadBuffer(tkOssClient, tkBucketName, objectName, buffer);
    }

    @Override
    public void uploadTK(String objectName, MultipartFile multipartFile) {
        uploadMultipartFile(tkOssClient, tkBucketName, objectName, multipartFile);
    }

    @Override
    public void uploadTK(String objectName, String filePath) {
        uploadLocalFile(tkOssClient, tkBucketName, objectName, filePath);
    }

    @Override
    public InputStream downloadTK(String objectName) {
        return downloadInputStream(tkOssClient, tkBucketName, objectName);
    }

    @Override
    public Boolean checkExistsTK(String objectName) {
        return checkObjectExists(tkOssClient, tkBucketName, objectName);
    }

    @Override
    public String getPublicPathTK(String objectName) {
        return getPublicPath(tkBucketHosts.get(0), objectName);
    }

    @Override
    public String getObjectNameTK(String publicPath) {
        return getObjectName(tkBucketHosts, publicPath);
    }

    @Override
    public String getObjectNameMark(String publicPath) {
        return getObjectName(markBucketHosts, publicPath);
    }

    @Override
    public void uploadMark(String objectName, byte[] buffer) {
        uploadBuffer(markOssClient, markBucketName, objectName, buffer);
    }

    @Override
    public String getPublicPathMark(String objectName) {
        return getPublicPath(markBucketHosts.get(0), objectName);
    }

    @Override
    public void downloadTKObjectsBuildZip(Collection<StrStrVo> fileNameObjectNameCollection, OutputStream outputStream) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(outputStream)) {
            for (StrStrVo nameUrl : fileNameObjectNameCollection) {
                try (InputStream is = downloadTK(nameUrl.getValue())) {
                    zos.putNextEntry(new ZipEntry(nameUrl.getKey()));
                    IOUtils.copy(is, zos);
                    zos.closeEntry();
                }
            }
            zos.finish();
        }
    }
}
