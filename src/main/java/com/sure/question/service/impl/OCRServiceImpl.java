package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperOcrRequest;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperOcrResponse;
import com.aliyun.teaopenapi.models.Config;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.ocr.aliyun.Figure;
import com.sure.question.dto.ocr.aliyun.Point;
import com.sure.question.dto.ocr.aliyun.paper.*;
import com.sure.question.service.OCRService;
import com.sure.question.util.ImageUtil;
import com.sure.question.util.PdfUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.WritableRaster;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class OCRServiceImpl implements OCRService {
    @Value("${aliyun.ocr.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.ocr.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.ocr.endpoint}")
    private String endpoint;
    private Client client;

    @Resource
    @Qualifier("ocrRequestExecutor")
    private ThreadPoolTaskExecutor ocrRequestExecutor;

    private static final Map<Integer, String> OCRParamSubjectMap = new HashMap<Integer, String>() {{
        put(1, "Chinese");
        put(2, "Math");
        put(3, "English");
        put(4, "Politics");
        put(15, "Politics");
        put(5, "History");
        put(6, "Geography");
        put(7, "Physics");
        put(8, "Chemistry");
        put(9, "Biology");
    }};
    private static final Map<Integer, String> OCRParamGradeLevelMap = new HashMap<Integer, String>() {{
        put(1, "PrimarySchool_");
        put(2, "JHighSchool_");
        put(4, "JHighSchool_");
    }};

    // 数理化中的字母数字尽量转为公式
    private static final Set<Integer> ConvertAsciiFormulaSubjects = Stream.of(2, 7, 8).collect(Collectors.toSet());

    private static final Pattern AsciiPattern = Pattern.compile("[\\x20-\\x7E∠△°]+");
    private static final Pattern LetterNumberPattern = Pattern.compile("[a-zA-Z0-9]");
    private static final Pattern TopicCodePattern = Pattern.compile("\\s*[一二三四五六七八九十]+\\s*、");
    private static final Pattern QuestionCodePattern = Pattern.compile("^\\s*[1-9][0-9]*\\s*[.．]");
    private static final Pattern BranchCodePattern = Pattern.compile("^\\s*(\\\\left)?\\s*[(（]\\s*[1-9][0-9]*\\s*(\\\\right)?\\s*[)）]");
    private static final Pattern OptionPattern = Pattern.compile("^\\s*[A-F]\\s*[.．]");
    private static final Pattern TextCirclePattern = Pattern.compile("\\\\textcircled\\s+[1-9]");
    private static final Pattern StartBecauseThereforePattern = Pattern.compile("^\\s*(\\$)?\\s*(∵|∴|\\\\because|\\\\therefore)");
    private static final Pattern StartDecimalPattern = Pattern.compile("^\\s*\\d+\\.\\d+($|\\s|[a-zA-Z°])");

    // pdf渲染图片分辨率
    private static final float DPI = 200F;

    @PostConstruct
    private void init() throws Exception {
        Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setEndpoint(endpoint);
        this.client = new Client(config);
    }

    /**
     * 将pdf转为html
     */
    @Override
    public String convertPdfToHtml(InputStream pdfFileStream, String tempFolder, int maxPage, int gradeLevelId, int subjectId) throws Exception {
        File outputDir = new File(tempFolder);
        if (!outputDir.exists()) {
            if (!outputDir.mkdirs()) {
                throw new IOException("无法创建文件夹: " + tempFolder);
            }
        }

        try {
            // 1. 将pdf转为图片
            List<String> imgPathList;
            imgPathList = PdfUtil.pdfToImage(pdfFileStream, DPI, maxPage, pageIndex -> tempFolder + (tempFolder.endsWith("/") ? "" : "/") + (pageIndex + 1) + ".png");

            // 2. 调ocr接口，并行执行
            List<String> ocrResponsePathList = new ArrayList<>();
            List<Future<?>> futures = new ArrayList<>();
            for (int i = 0; i < imgPathList.size(); i++) {
                int finalIndex = i;
                String imgPath = imgPathList.get(i);
                ocrResponsePathList.add(null);
                Future<?> future = ocrRequestExecutor.submit(() -> {
                    try {
                        String ocrResponse = recognizeEduPaperOCR(FileUtils.openInputStream(new File(imgPath)) , gradeLevelId, subjectId);
                        // 保存识别结果
                        String jsonPath = imgPath.replace(".png", ".json");
                        ocrResponsePathList.set(finalIndex, jsonPath);
                        Files.write(Paths.get(jsonPath), ocrResponse.getBytes(StandardCharsets.UTF_8));
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }
                });
                futures.add(future);
            }
            for (Future<?> future : futures) {
                future.get();
            }

            // 3. 解析ocr结果为html
            return parseOCRResponse(imgPathList, ocrResponsePathList, subjectId);
        } finally {
            FileUtils.deleteDirectory(outputDir);
        }
    }

    /**
     * 调ocr接口
     */
    private String recognizeEduPaperOCR(InputStream imgFileInputStream, int gradeLevelId, int subjectId) throws Exception {
        RecognizeEduPaperOcrRequest request = new RecognizeEduPaperOcrRequest();
        request.setBody(imgFileInputStream);
        request.setImageType("scan");
        request.setSubject(getOCRSubjectParam(gradeLevelId, subjectId));
        request.setOutputOricoord(false);
        RecognizeEduPaperOcrResponse response = client.recognizeEduPaperOcr(request);
        if (response.getStatusCode() != 200) {
            throw new ApiException("ocr接口返回失败, 错误码: " + response.getStatusCode());
        }
        return response.getBody().getData();
    }

    private String getOCRSubjectParam(int gradeLevelId, int subjectId) {
        String subjectName = OCRParamSubjectMap.get(subjectId);
        if (StringUtils.isEmpty(subjectName)) {
            return "default";
        }
        String gradeLevelName = OCRParamGradeLevelMap.get(gradeLevelId);
        if (StringUtils.isEmpty(gradeLevelName)) {
            return subjectName;
        }
        return gradeLevelName + subjectName;
    }

    /**
     * 解析ocr结果为html并保存
     */
    @Override
    public String parseOCRResponse(List<String> imgPathList, List<String> ocrResponsePathList, int subjectId) throws IOException {
        String emptyHtml = "<html lang=\"zh-CN\"><head><meta charset=\"UTF-8\"></head><body></body></html>";
        Document doc = Jsoup.parse(emptyHtml);

        for (int i = 0; i < imgPathList.size(); i++) {
            BufferedImage pageImg = ImageIO.read(new File(imgPathList.get(i)));
            String ocrResponse = FileUtils.readFileToString(new File(ocrResponsePathList.get(i)), StandardCharsets.UTF_8);
            parseOnePageOCRResultAddInToDocument(doc, ocrResponse, pageImg, subjectId);
        }
        return doc.body().html();
    }

    /**
     * 解析一页ocr结果，添加到html文档中
     */
    private void parseOnePageOCRResultAddInToDocument(Document doc, String bodyJson, BufferedImage pageImg, int subjectId) throws IOException {
        PaperBody body = JSON.parseObject(bodyJson, PaperBody.class);

        List<WordInfo> wordInfoList = body.getPrism_wordsInfo();

        // 1. 计算每个字块的位置
        wordInfoList.forEach(wordInfo -> calcPosition(wordInfo.getPos(), wordInfo));

        // 2. 创建图片和表格，表格暂时按图片处理
        List<Image> imageList = createImages(body.getFigure(), wordInfoList, pageImg);
        List<Table> tableList = new ArrayList<>();
//        List<Table> tableList = createTables(body.getFigure(), wordInfoList);

        // 3. 分栏
        List<List<IElement>> columns = splitColumns(wordInfoList, imageList, tableList, body.getWidth(), pageImg);

        // 4. 各栏分别分行
        List<List<List<IElement>>> columnRows = columns.stream().map(this::mergeIntoRows).collect(Collectors.toList());

        // 5. 整理内容
        cleanContent(columnRows, subjectId);

        // 6. 分段
        List<List<List<IElement>>> paragraphs = mergeParagraphs(columnRows);

        // 7. 添加到文档中
        paragraphs.forEach(p -> addParagraphToDocument(p, doc));
    }

    /**
     * 计算字块位置
     */
    private void calcPosition(List<Point> points, IElement element) {
        int left = Integer.MAX_VALUE;
        int right = 0;
        int top = Integer.MAX_VALUE;
        int bottom = 0;
        for (Point p : points) {
            if (p.getX() < left) {
                left = p.getX();
            }
            if (p.getX() > right) {
                right = p.getX();
            }
            if (p.getY() < top) {
                top = p.getY();
            }
            if (p.getY() > bottom) {
                bottom = p.getY();
            }
        }
        element.setLeft(left);
        element.setRight(right);
        element.setTop(top);
        element.setBottom(bottom);
    }

    /**
     * 创建图片
     */
    private List<Image> createImages(List<Figure> figures, List<WordInfo> wordInfoList, BufferedImage pageImg) throws IOException {
        List<Image> result = new ArrayList<>();
        for (Figure figure : figures) {
            if (!figure.isImage() && !figure.isTable()) {
                continue;
            }
            Image img = new Image();
            calcPosition(figure.getPoints(), img);

            // 截取子图，上下左右扩展2mm
            int expand = MM2Pixel(2);
            int x = Math.max(0, img.getLeft() - expand);
            int y = Math.max(0, img.getTop() - expand);
            int width = Math.min(pageImg.getWidth() - x, img.getRight() - x + expand * 2 + 1);
            int height = Math.min(pageImg.getHeight() - y, img.getBottom() - y + expand * 2 + 1);
            BufferedImage croppedImage = pageImg.getSubimage(x, y, width, height);

            // dataUrl
            img.setSrc(ImageUtil.toBase64DataUrl(croppedImage, "png"));

            // 图中文字块
            img.setWords(getImageOrTableWords(img, wordInfoList));
            result.add(img);
        }
        return result;
    }

    /**
     * 毫米转像素
     */
    private int MM2Pixel(float mm) {
        return (int) Math.round(mm / 25.4 * OCRServiceImpl.DPI);
    }

    /**
     * 像素转毫米
     */
    private float Pixel2MM(int px) {
        return px / OCRServiceImpl.DPI * 25.4F;
    }

    /**
     * 屏幕像素，默认一英寸120像素
     */
    private int getScreenPixel(int px) {
        return Math.round(px / OCRServiceImpl.DPI * 120);
    }

    /**
     * 创建表格
     */
    private List<Table> createTables(List<Figure> figures, List<WordInfo> wordInfoList) {
        List<Table> result = new ArrayList<>();
        for (Figure figure : figures) {
            if (!figure.isTable()) {
                continue;
            }
            Table table = new Table();
            calcPosition(figure.getPoints(), table);
            int expand = 10;
            table.setLeft(table.getLeft() - expand);
            table.setTop(table.getTop() - expand);
            table.setRight(table.getRight() + expand * 2);
            table.setBottom(table.getBottom() + expand * 2);
            table.setWords(getImageOrTableWords(table, wordInfoList));
            result.add(table);
        }
        return result;
    }

    /**
     * 找与图片/表格区域存在交集的字块，并从字块列表中移出
     */
    private List<WordInfo> getImageOrTableWords(IElement element, List<WordInfo> wordInfoList) {
        List<WordInfo> result = new ArrayList<>();
        Iterator<WordInfo> iterator = wordInfoList.iterator();
        while (iterator.hasNext()) {
            WordInfo wordInfo = iterator.next();
            boolean intersect = wordInfo.getLeft() <= element.getRight()
                    && wordInfo.getRight() >= element.getLeft()
                    && wordInfo.getTop() <= element.getBottom()
                    && wordInfo.getBottom() >= element.getTop();
            if (intersect) {
                result.add(wordInfo);
                iterator.remove();
            }
        }
        return result;
    }

    /**
     * 分栏，返回各栏子块列表
     */
    private List<List<IElement>> splitColumns(List<WordInfo> wordInfoList, List<Image> imageList, List<Table> tableList, int pageWidth, BufferedImage pageImg) {
        List<IElement> elements = Stream.of(wordInfoList, imageList, tableList).flatMap(Collection::stream).collect(Collectors.toList());

        // 可能的栏数
        List<List<SplitLine>> possibleSplitLinesList = new ArrayList<>();
        float pageWidthPixel = Pixel2MM(pageWidth);
        // 假设宽度超过30cm才可能分三栏
        if (pageWidthPixel > 300F) {
            possibleSplitLinesList.add(createSplitLines(3, pageWidth));
        }
        // 假设宽度超过15cm才可能分两栏
        if (pageWidthPixel > 150F) {
            possibleSplitLinesList.add(createSplitLines(2, pageWidth));
        }
        if (possibleSplitLinesList.isEmpty()) {
            return Collections.singletonList(elements);
        }

        // 检测页面分栏，先尝试分栏线，再尝试空白区域
        List<SplitLine> pageSplitLines = splitByLine(possibleSplitLinesList, pageImg);
        if (pageSplitLines == null) {
            pageSplitLines = splitBySpace(possibleSplitLinesList, elements);
        }

        // 不分栏
        if (pageSplitLines == null) {
            return Collections.singletonList(elements);
        }

        // 各栏取其左、右边界间的内容
        List<List<IElement>> result = new ArrayList<>();
        for (int i = 0; i <= pageSplitLines.size(); i++) {
            int leftBoundary = i > 0 ? pageSplitLines.get(i - 1).line : 0;
            int rightBoundary = i < pageSplitLines.size() ? pageSplitLines.get(i).line : Integer.MAX_VALUE;
            List<IElement> columnElements = elements.stream().filter(element -> element.getLeft() >= leftBoundary
                    && element.getRight() <= rightBoundary).collect(Collectors.toList());
            result.add(columnElements);
        }
        return result;
    }

    /**
     * 分栏线
     */
    private static class SplitLine {
        // 分栏线左边界X坐标
        public int left;
        // 分栏线右边界X坐标
        public int right;
        // 分栏线所在X坐标
        public int line;
    }

    /**
     * 区域
     */
    private static class Range {
        public int left;
        public int right;
    }

    /**
     * 创建分栏线
     */
    private List<SplitLine> createSplitLines(int columns, int pageWidth) {
        List<SplitLine> result = new ArrayList<>();
        int lineCount = columns - 1;
        if (lineCount <= 0) {
            return result;
        }
        int widthPerColumn = pageWidth / columns;
        // 左右扩展5%
        int expandLeftRight = (int) (pageWidth * 0.05);
        for (int i = 0; i < columns - 1; i++) {
            int center = widthPerColumn * (i + 1);
            SplitLine splitLine = new SplitLine();
            splitLine.left = center - expandLeftRight;
            splitLine.right = center + expandLeftRight;
            result.add(splitLine);
        }
        return result;
    }

    /**
     * 按分栏线分栏
     */
    private List<SplitLine> splitByLine(List<List<SplitLine>> possibleSplitLinesList, BufferedImage pageImg) {
        // 超过页面高度75%的黑色竖直线认为是分栏线
        int threshold = (int) (pageImg.getHeight() * 0.75);
        for (List<SplitLine> splitLines : possibleSplitLinesList) {
            int count = 0;
            for (SplitLine splitLine : splitLines) {
                boolean find = findSplitLine(splitLine, pageImg, threshold);
                if (!find) {
                    break;
                }
                count++;
            }
            if (count == splitLines.size()) {
                return splitLines;
            }
        }
        return null;
    }

    /**
     * 找分栏线
     */
    private boolean findSplitLine(SplitLine splitLine, BufferedImage pageImg, int threshold) {
        WritableRaster raster = pageImg.getRaster();
        int[] pixel = new int[3];
        int pageImgHeight = pageImg.getHeight();
        // 超过maxY，即便下面全部为黑线，其长度也达不到阈值
        int maxY = pageImgHeight - threshold;
        // 超过maxGap的两个黑点认为不连续
        int maxGap = 4;

        for (int x = splitLine.left; x <= splitLine.right; x++) {
            int start = -1;
            int end = 0;
            // 找连续的黑线
            for (int y = 0; y < pageImgHeight; y++) {
                raster.getPixel(x, y, pixel);
                if (pixel[0] <= 192 && pixel[1] <= 192 && pixel[2] <= 192) {
                    if (start == -1) {
                        start = y;
                    }
                    end = y;
                } else {
                    if (y - end <= maxGap) {
                        continue;
                    }
                    if (y > maxY) {
                        break;
                    }
                    start = -1;
                    end = 0;
                }
                if (end - start >= threshold) {
                    splitLine.line = x;
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 按空白区域分栏
     */
    private List<SplitLine> splitBySpace(List<List<SplitLine>> possibleSplitLinesList, List<IElement> elements) {
        // 超过10mm的连续空白认为是分栏空白
        int threshold = MM2Pixel(10);
        for (List<SplitLine> splitLines : possibleSplitLinesList) {
            int count = 0;
            for (SplitLine splitLine : splitLines) {
                if (!findSplitSpace(splitLine, elements, threshold)) {
                    break;
                }
                count++;
            }
            if (count == splitLines.size()) {
                return splitLines;
            }
        }
        return null;
    }

    /**
     * 找分栏空白区域
     */
    private boolean findSplitSpace(SplitLine splitLine, List<? extends IElement> elementList, int threshold) {
        // 找纵向上没有内容的区域
        List<Range> ranges = new ArrayList<>();
        Range range = null;
        for (int x = splitLine.left; x <= splitLine.right; x++) {
            // 该X坐标竖线上是否没有字块
            int finalX = x;
            boolean isEmpty = elementList.stream().allMatch(element -> element.getLeft() > finalX || element.getRight() < finalX);
            if (!isEmpty) {
                range = null;
            } else {
                if (range == null) {
                    range = new Range();
                    range.left = x;
                    ranges.add(range);
                }
                range.right = x;
            }
        }
        // 宽度要达到阈值
        ranges.removeIf(r -> r.right - r.left < threshold);
        if (ranges.isEmpty()) {
            return false;
        }
        // 取最宽的空白区域的中间位置作为分割线
        ranges.sort(Comparator.comparing(r -> r.left - r.right));
        splitLine.line = (ranges.get(0).left + ranges.get(0).right) / 2;
        return true;
    }

    /**
     * 合并成行
     */
    private List<List<IElement>> mergeIntoRows(List<IElement> elements) {
        // 区分文字与图表
        List<IElement> words = new ArrayList<>();
        List<IElement> imageOrTables = new ArrayList<>();
        for (IElement element : elements) {
            if (element.isWord()) {
                words.add(element);
            } else {
                imageOrTables.add(element);
            }
        }

        // 文字行
        List<List<IElement>> rows = mergeOneTypeContentIntoRows(words);

        // 图表行
        imageOrTables.sort(Comparator.comparing(IElement::getTop));
        List<List<IElement>> imageOrTableRows = mergeOneTypeContentIntoRows(imageOrTables);

        // 图表行加入文字行中
        for (List<IElement> imageOrTableRow : imageOrTableRows) {
            int bottom = imageOrTableRow.stream().mapToInt(IElement::getBottom).max().orElse(Integer.MAX_VALUE);
            int insertIndex = 0;
            for (List<IElement> row : rows) {
                int top = row.stream().mapToInt(IElement::getTop).min().orElse(0);
                if (top > bottom) {
                    break;
                }
                insertIndex++;
            }
            rows.add(insertIndex, imageOrTableRow);
        }

        return rows;
    }
    private List<List<IElement>> mergeOneTypeContentIntoRows(List<IElement> elements) {
        List<List<IElement>> rows = new ArrayList<>();
        List<IElement> row = null;
        int rowBottom = 0;
        for (IElement element : elements) {
            if (row == null || element.getTop() > rowBottom) {
                row = new ArrayList<>();
                row.add(element);
                rows.add(row);
                rowBottom = element.getBottom();
            } else {
                row.add(element);
                rowBottom = Math.max(rowBottom, element.getBottom());
            }
        }
        rows.forEach(rowElements -> rowElements.sort(Comparator.comparing(IElement::getLeft)));
        return rows;
    }

    /**
     * 整理内容
     */
    private void cleanContent(List<List<List<IElement>>> columnRows, int subjectId) {
        List<List<IElement>> allRows = columnRows.stream().flatMap(Collection::stream).collect(Collectors.toList());
        // 1. 清理文字
        cleanText(allRows);
        // 2. 字母数字转为公式
        if (ConvertAsciiFormulaSubjects.contains(subjectId)) {
            allRows.forEach(this::convertAsciiFormula);
        }
        // 3. 清理公式
        cleanFormula(allRows);
        // 删除空行
        columnRows.forEach(column -> column.removeIf(Collection::isEmpty));
    }

    private void cleanText(List<List<IElement>> rows) {
        // 1. 添加填空题下划线
        rows.forEach(row -> row.forEach(this::addUnderline));
        // 2. 去除文字块末尾与后续文字块开头重复的逗号
        rows.forEach(this::removeDuplicateEndChar);
    }

    private void addUnderline(IElement element) {
        if (!element.isWord()) {
            return;
        }
        WordInfo wordInfo = (WordInfo) element;
        if (wordInfo.isRecClassifyFormula()) {
            return;
        }
        List<CharInfo> charInfoList = wordInfo.getCharInfo();
        if (charInfoList.size() <= 2) {
            return;
        }
        // 算平均间距
        int avgGap = (charInfoList.get(charInfoList.size() - 1).getX() - charInfoList.get(0).getX()) / (charInfoList.size() - 1);

        // 字符间距超过平均间距的某个倍数，即认为有下划线
        int threshold = (int) (avgGap * 2.5);
        boolean hasUnderline = false;
        StringBuilder sb = new StringBuilder();
        CharInfo leftChar = null;
        for (CharInfo charInfo : wordInfo.getCharInfo()) {
            if (leftChar != null && charInfo.getX() - leftChar.getX() > threshold) {
                sb.append("______");
                hasUnderline = true;
            }
            sb.append(charInfo.getWord());
            leftChar = charInfo;
        }
        if (hasUnderline) {
            wordInfo.setWord(sb.toString());
        }
    }

    private void removeDuplicateEndChar(List<IElement> row) {
        WordInfo preWordInfo = null;
        for (IElement element : row) {
            if (!element.isWord()) {
                preWordInfo = null;
                continue;
            }
            WordInfo thisWordInfo = (WordInfo) element;
            if (preWordInfo != null) {
                boolean isPreFormula = preWordInfo.isRecClassifyFormula();
                boolean isThisFormula = thisWordInfo.isRecClassifyFormula();
                String preText = preWordInfo.getWord();
                String thisText = thisWordInfo.getWord();
                if (StringUtils.isNotEmpty(preText) && StringUtils.isNotEmpty(thisText)) {
                    String preEnd = preText.substring(preText.length() - 1);
                    String thisStart = thisText.substring(0, 1);
                    List<String[]> mayDuplicateStrList = Arrays.asList(
                            new String[]{",", "，"},
                            new String[]{";", "；"},
                            new String[]{".", "．"},
                            new String[]{"、"},
                            new String[]{"(", "（"},
                            new String[]{")", "）"},
                            new String[]{"["},
                            new String[]{"]"},
                            new String[]{"{"},
                            new String[]{"}"});
                    boolean equals = mayDuplicateStrList.stream().anyMatch(list -> StringUtils.equalsAny(preEnd, list) && StringUtils.equalsAny(thisStart, list));
                    if (!equals && ((isPreFormula && !isThisFormula) || (!isPreFormula && isThisFormula))) {
                        equals = StringUtils.equals(preEnd, thisStart);
                    }
                    if (equals) {
                        if (isPreFormula && Stream.of(")", "]", "}").noneMatch(preEnd::endsWith)) {
                            preWordInfo.setWord(preText.substring(0, preText.length() - 1));
                        } else {
                            thisWordInfo.setWord(thisText.substring(1));
                        }
                    }
                }
            }
            preWordInfo = thisWordInfo;
        }
    }

    private void cleanFormula(List<List<IElement>> rows) {
        // 1. 替换圈码
        rows.forEach(this::replaceTextCircled);
        // 2. 换行引起的公式内括号不成对
        fixBracketsPair(rows);
        // 3. 移除公式中题号小题号选项标签
        rows.forEach(this::removeFormulaQuestionBranchCodeOptionLabel);
    }

    private void replaceTextCircled(List<IElement> row) {
        for (IElement element : row) {
            if (!element.isWord()) {
                continue;
            }
            WordInfo wordInfo = (WordInfo) element;
            if (!wordInfo.isRecClassifyFormula()) {
                continue;
            }
            Matcher matcher = TextCirclePattern.matcher(wordInfo.getWord());
            StringBuffer sb = new StringBuffer();
            boolean find = false;
            while (matcher.find()) {
                find = true;
                String matched = matcher.group();
                int n = Integer.parseInt(matched.substring(matched.length() - 1));
                String replacement = String.valueOf((char) (9311 + n));
                matcher.appendReplacement(sb, replacement);
            }
            if (find) {
                matcher.appendTail(sb);
                wordInfo.setWord(sb.toString());
            }
        }
    }

    private void fixBracketsPair(List<List<IElement>> rows) {
        List<IElement> preRow = null;
        for (List<IElement> thisRow : rows) {
            fixBracketsPairNearRows(preRow, thisRow);
            preRow = thisRow;
        }
        rows.removeIf(List::isEmpty);
    }

    private void fixBracketsPairNearRows(List<IElement> preRow, List<IElement> thisRow) {
        if (preRow == null || preRow.isEmpty() || thisRow == null || thisRow.isEmpty()) {
            return;
        }
        IElement preRowEndElement = preRow.get(preRow.size() - 1);
        IElement thisRowStartElement = thisRow.get(0);
        if (!preRowEndElement.isWord() || !thisRowStartElement.isWord()) {
            return;
        }
        WordInfo preWordInfo = (WordInfo) preRowEndElement;
        WordInfo thisWordInfo = (WordInfo) thisRowStartElement;
        if (!preWordInfo.isRecClassifyFormula() || !thisWordInfo.isRecClassifyFormula()) {
            return;
        }
        Map<String, Integer> preMap = parseBracketsNotInPairCount(preWordInfo.getWord());
        if (preMap == null) {
            return;
        }
        Map<String, Integer> thisMap = parseBracketsNotInPairCount(thisWordInfo.getWord());
        if (thisMap == null) {
            return;
        }
        if (preMap.size() != thisMap.size()) {
            return;
        }
        boolean matched = preMap.keySet().stream().allMatch(key -> {
            Integer preCount = preMap.get(key);
            Integer thisCount = thisMap.get(key);
            return preCount != null && thisCount != null && preCount + thisCount == 0;
        });
        if (matched) {
            preWordInfo.setWord(preWordInfo.getWord() + thisWordInfo.getWord());
            thisRow.remove(0);
        }
    }

    private void removeFormulaQuestionBranchCodeOptionLabel(List<IElement> row) {
        int index = 0;
        for (IElement element : row) {
            index++;
            if (!element.isWord()) {
                continue;
            }
            WordInfo wordInfo = (WordInfo) element;
            if (!wordInfo.isRecClassifyFormula()) {
                continue;
            }
            String word = wordInfo.getWord();
            String head = getFormulaQuestionBranchCodeOptionLabel(word, index == 1);
            if (StringUtils.isNotEmpty(head)) {
                wordInfo.setRecClassify(1);
                String formula = word.substring(head.length());
                head = cleanQuestionBranchCodeOptionLabel(head);
                wordInfo.setWord(head + "$" + formula + "$");
            }
        }
    }

    private String getFormulaQuestionBranchCodeOptionLabel(String formula, boolean isRowStart) {
        StringBuilder head = new StringBuilder();
        String tail = formula;
        List<Pattern> patterns = new ArrayList<>();
        if (isRowStart) {
            patterns.add(QuestionCodePattern);
            patterns.add(BranchCodePattern);
        }
        patterns.add(OptionPattern);
        Matcher matcher;
        for (Pattern pattern : patterns) {
            matcher = pattern.matcher(tail);
            if (matcher.find()) {
                // 区分题号与小数
                if (pattern == QuestionCodePattern) {
                    if (StartDecimalPattern.matcher(tail).find()) {
                        continue;
                    }
                }
                head.append(matcher.group());
                tail = formula.substring(head.length());
            }
        }
        return head.toString();
    }

    private String cleanQuestionBranchCodeOptionLabel(String head) {
        return head.replace("\\left", "").replace("\\right", "").replace(" ", "");
    }

    private void convertAsciiFormula(List<IElement> row) {
        int index = 0;
        for (IElement element : row) {
            index++;
            if (!element.isWord()) {
                continue;
            }
            WordInfo wordInfo = (WordInfo) element;
            if (wordInfo.isRecClassifyFormula()) {
                continue;
            }
            convertToFormula(wordInfo, index == 1);
        }
    }

    private void convertToFormula(WordInfo wordInfo, boolean isRowStart) {
        String word = wordInfo.getWord();
        StringBuffer sb = new StringBuffer();
        Matcher asciiMatcher = AsciiPattern.matcher(word);
        boolean converted = false;
        while (asciiMatcher.find()) {
            String matched = asciiMatcher.group();
            // 必须要有字母或数字
            if (!LetterNumberPattern.matcher(matched).find()) {
                continue;
            }
            String text = "";
            if (asciiMatcher.start() == 0) {
                text = getFormulaQuestionBranchCodeOptionLabel(matched, isRowStart);
            }
            String formula = matched;
            if (StringUtils.isNotEmpty(text)) {
                formula = matched.substring(text.length());
                text = cleanQuestionBranchCodeOptionLabel(text);
            }
            if (StringUtils.isNotEmpty(formula)) {
                formula = formula.replace("Rt△", "\\rm{Rt}\\triangle ");
                // latex转义
                formula = formula.replace("#", "\\#")
                        .replace("%", "\\%")
                        .replace("$", "\\$")
                        .replace("&", "\\&")
                        .replace("{", "\\{")
                        .replace("}", "\\}")
                        .replace(" ", "\\ ");
                asciiMatcher.appendReplacement(sb, text + "\\$" + Matcher.quoteReplacement(formula) + "\\$");
                converted = true;
            }
        }
        if (converted) {
            asciiMatcher.appendTail(sb);
            wordInfo.setWord(sb.toString());
        }
    }

    /**
     * 解析一段文本中圆括号、方括号、花括号不成对的个数
     */
    private Map<String, Integer> parseBracketsNotInPairCount(String text) {
        int roundCount = 0;
        int squareCount = 0;
        int curlyCount = 0;
        for (char c : text.toCharArray()) {
            switch (c) {
                case '(': {
                    roundCount++;
                    break;
                }
                case ')': {
                    roundCount--;
                    break;
                }
                case '[': {
                    squareCount++;
                    break;
                }
                case ']': {
                    squareCount--;
                    break;
                }
                case '{': {
                    curlyCount++;
                    break;
                }
                case '}': {
                    curlyCount--;
                    break;
                }
                default: break;
            }
        }

        if (roundCount == 0 && squareCount == 0 && curlyCount == 0) {
            return null;
        }
        Map<String, Integer> result = new HashMap<>();
        if (roundCount != 0) {
            result.put("round", roundCount);
        }
        if (squareCount != 0) {
            result.put("square", squareCount);
        }
        if (curlyCount != 0) {
            result.put("curly", curlyCount);
        }
        return result;
    }

    /**
     * 分段
     */
    private List<List<List<IElement>>> mergeParagraphs(List<List<List<IElement>>> columnRows) {
        List<List<List<IElement>>> result = new ArrayList<>();
        List<List<IElement>> p = null;
        boolean preRowNearRightBorder = false;
        List<Pattern> newParagraphPatterns = Arrays.asList(TopicCodePattern, QuestionCodePattern, BranchCodePattern, OptionPattern, StartBecauseThereforePattern);
        for (List<List<IElement>> column : columnRows) {
            int columnRightBorder = column.stream().flatMap(Collection::stream).filter(IElement::isWord).mapToInt(IElement::getRight).max().orElse(0);
            int nearRightBorderThreshold = columnRightBorder - MM2Pixel(6);
            for (List<IElement> row : column) {
                IElement firstElement = row.get(0);
                boolean sameParagraph = p != null && preRowNearRightBorder && firstElement.isWord();
                if (sameParagraph) {
                    String firstWord = ((WordInfo) firstElement).getWord();
                    sameParagraph = newParagraphPatterns.stream().noneMatch(pattern -> pattern.matcher(firstWord).find());
                }
                if (!sameParagraph) {
                    p = new ArrayList<>();
                    result.add(p);
                }
                p.add(row);
                IElement lastElement = row.get(row.size() - 1);
                preRowNearRightBorder = lastElement.isWord() && lastElement.getRight() > nearRightBorderThreshold;
            }
        }
        return result;
    }

    /**
     * 添加一段到文档中
     */
    private void addParagraphToDocument(List<List<IElement>> rows, Document doc) {
        Element p = doc.createElement("p");
        for (List<IElement> row : rows) {
            IElement pre = null;
            for (IElement element : row) {
                checkAndAppendHtmlSpace(pre, element, doc, p);
                appendHtmlNode(element, doc, p);
                pre = element;
            }
        }
        doc.body().appendChild(p);
    }

    private void checkAndAppendHtmlSpace(IElement preElement, IElement thisElement, Document doc, Element parent) {
        if (preElement == null || !preElement.isWord() || thisElement == null || !thisElement.isWord()) {
            return;
        }
        int minSpaceMM = 15;
        int threshold = MM2Pixel(minSpaceMM);
        int space = thisElement.getLeft() - preElement.getRight();
        if (space > threshold) {
            Element span = doc.createElement("span");
            int width = getScreenPixel(space) / 2;
            span.attr("style", "display:inline-block;width:" + width + "px");
            parent.appendChild(span);
        }
    }

    /**
     * 添加一个节点
     */
    private void appendHtmlNode(IElement element, Document doc, Element parent) {
        if (element.isWord()) {
            WordInfo wordInfo = (WordInfo) element;
            if (wordInfo.isRecClassifyFormula()) {
                parent.appendText("$" + wordInfo.getWord() + "$");
            } else {
                parent.appendText(wordInfo.getWord());
            }
        } else if (element.isImage()) {
            Image image = (Image) element;
            Element img = doc.createElement("img");
            img.attr("src", image.getSrc());
            int width = getScreenPixel(image.getRight() - image.getLeft() + 1);
            int height = getScreenPixel(image.getBottom() - image.getTop() + 1);
            img.attr("width", String.valueOf(width));
            img.attr("height", String.valueOf(height));
            parent.appendChild(img);
        } else if (element.isTable()) {
            Table table = (Table) element;
            Element t = doc.createElement("table");
            for (List<List<WordInfo>> row : table.getRows()) {
                Element tr = doc.createElement("tr");
                t.appendChild(tr);
                for (List<WordInfo> cell : row) {
                    Element td = doc.createElement("td");
                    tr.appendChild(td);
                    for (WordInfo wordInfo : cell) {
                        appendHtmlNode(wordInfo, doc, td);
                    }
                }
            }
            parent.appendChild(t);
        }
    }
}



