package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.entity.CoachBookIngestion;
import com.sure.question.entity.CoachBookIngestionStructure;
import com.sure.question.enums.CommonProgressStatusEnum;
import com.sure.question.enums.FileTypeEnum;
import com.sure.question.mapper.CoachBookIngestionStructureMapper;
import com.sure.question.service.CoachBookIngestionService;
import com.sure.question.service.CoachBookIngestionUploadService;
import com.sure.question.service.OssManager;
import com.sure.question.util.IdUtil;
import com.sure.question.util.ImageUtil;
import com.sure.question.util.MultipartFileUtil;
import com.sure.question.vo.TokenUserVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CoachBookIngestionUploadServiceImpl implements CoachBookIngestionUploadService {
    private final CoachBookIngestionService coachBookIngestionService;
    private final CoachBookIngestionStructureMapper coachBookIngestionStructureMapper;
    private final OssManager ossManager;
    private final RedisTemplate<String, ?> redisTemplate;

    @Resource
    @Qualifier("pdfToImageExecutor")
    private ThreadPoolTaskExecutor pdfToImageExecutor;
    @Value("${aliyun.ossTkImg.prefix.coachBookIngestion}")
    private String coachBookIngestionPathPrefix;

    @Override
    public void changeFileType(int ingestionId, String fileType, TokenUserVo tokenUserVo) {
        if (!FileTypeEnum.PDF.getId().equals(fileType) && !FileTypeEnum.IMAGE.getId().equals(fileType)) {
            throw new ApiException("上传文件类型错误，仅支持pdf和图片");
        }
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckPermission(ingestionId, tokenUserVo);
        if (ingestion.getFinished()) {
            throw new ApiException("录入已完成，不允许修改上传文件类型");
        }
        if (ingestion.getDeleted()) {
            throw new ApiException("该录入项目已删除");
        }
        if (StringUtils.isNotEmpty(ingestion.getFilePath())) {
            throw new ApiException("已上传文件，不允许修改上传文件类型");
        }
        if (StringUtils.equals(fileType, ingestion.getFileType())) {
            throw new ApiException("上传文件类型未改变");
        }
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getFileType, fileType)
                .update();
    }


    @Override
    public String uploadPdf(int ingestionId, MultipartFile file, boolean autoConvertImage, TokenUserVo userVo) {
        if (!MultipartFileUtil.isPdf(file)) {
            throw new ApiException("不是pdf文件");
        }
        getIngestionCheckCanUpload(ingestionId, FileTypeEnum.PDF, userVo);
        String path = coachBookIngestionPathPrefix + ingestionId + "/upload/" + RandomStringUtils.randomAlphabetic(8).toLowerCase() + ".pdf";
        ossManager.uploadTK(path, file);
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getFilePath, path)
                .set(CoachBookIngestion::getPageImages, null)
                .set(CoachBookIngestion::getPdfPageCount, null)
                .set(CoachBookIngestion::getPdfToImageStatus, CommonProgressStatusEnum.INIT.getId())
                .set(CoachBookIngestion::getUploadBy, userVo.getUserId())
                .set(CoachBookIngestion::getUploadTime, new Date())
                .update();
        if (autoConvertImage) {
            // 更新状态为进行中
            coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                    .set(CoachBookIngestion::getPdfToImageStatus, CommonProgressStatusEnum.RUNNING.getId())
                    .update();
            pdfToImageExecutor.submit(() -> convertPdfToImages(ingestionId));
        }
        return ossManager.getPublicPathTK(path);
    }



    private CoachBookIngestion getIngestionCheckFileType(int ingestionId, FileTypeEnum fileType, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);
        if (!fileType.getId().equals(ingestion.getFileType())) {
            throw new ApiException("上传文件类型错误");
        }
        return ingestion;
    }

    private CoachBookIngestion getIngestionCheckCanUpload(int ingestionId, FileTypeEnum fileType, TokenUserVo userVo) {
        CoachBookIngestion ingestion = getIngestionCheckFileType(ingestionId, fileType, userVo);
        // pdf不允许再次上传
        if (FileTypeEnum.PDF == fileType && StringUtils.isNotEmpty(ingestion.getFilePath())) {
            if (CommonProgressStatusEnum.RUNNING.getId().equals(ingestion.getPdfToImageStatus())) {
                throw new ApiException("pdf文件正在转为图片，此时不允许再次上传");
            }
            if (existsStructure(ingestionId)) {
                throw new ApiException("已经上传过文件且已经解析结构，不允许再次上传");
            }
        }
        return ingestion;
    }

    private boolean existsStructure(int ingestionId) {
        return coachBookIngestionStructureMapper.selectCount(new LambdaQueryWrapper<CoachBookIngestionStructure>()
                .eq(CoachBookIngestionStructure::getIngestionId, ingestionId)) > 0;
    }

    private void convertPdfToImages(int ingestionId) {
        CoachBookIngestion ingestion = coachBookIngestionService.getBaseMapper().selectById(ingestionId);
        if (ingestion == null) {
            return;
        }
        if (!FileTypeEnum.PDF.getId().equals(ingestion.getFileType())) {
            return;
        }
        if (StringUtils.isEmpty(ingestion.getFilePath())) {
            return;
        }
        if (!CommonProgressStatusEnum.RUNNING.getId().equals(ingestion.getPdfToImageStatus())) {
            return;
        }

        // 保留已有的图片
        List<PageImage> pageImages = coachBookIngestionService.parsePageImages(ingestion.getPageImages(), false);

        // 下载pdf文件并解析
        try (InputStream inputStream = ossManager.downloadTK(ingestion.getFilePath());
             PDDocument document = Loader.loadPDF(IOUtils.toByteArray(inputStream))) {
            // 更新pdf页数
            if (ingestion.getPdfPageCount() == null || ingestion.getPdfPageCount() != document.getNumberOfPages()) {
                coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                        .set(CoachBookIngestion::getPdfPageCount, document.getNumberOfPages())
                        .update();
            }
            // 逐页转为图片并上传至OSS
            int startPageIndex = pageImages.size();
            String basePath = ingestion.getFilePath().replace(".pdf", "");
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            for (int page = startPageIndex; page < document.getNumberOfPages(); page++) {
                BufferedImage image = pdfRenderer.renderImageWithDPI(page, 200);
                byte[] imageBytes = ImageUtil.toByteArray(image, "jpg");

                String path = basePath + "/" + (page + 1) + ".jpg";
                ossManager.uploadTK(path, imageBytes);

                PageImage pageImage = new PageImage(path, image.getWidth(), image.getHeight(), null);
                pageImages.add(pageImage);
                savePageImages(ingestionId, pageImages, CommonProgressStatusEnum.RUNNING);
            }
            savePageImages(ingestionId, pageImages, CommonProgressStatusEnum.COMPLETED);
        } catch (Exception e) {
            log.error("pdf转图片失败, ingestionId = {}\n", ingestionId, e);
            savePageImages(ingestionId, pageImages, CommonProgressStatusEnum.FAILED);
        }
    }

    private void savePageImages(int ingestionId, List<PageImage> pageImages, CommonProgressStatusEnum status) {
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getPdfToImageStatus, status.getId())
                .set(CoachBookIngestion::getPageImages, JSON.toJSONString(pageImages))
                .update();
    }

    @Override
    public void convertPdfToImage(int ingestionId, TokenUserVo userVo) {
        CoachBookIngestion ingestion = getIngestionCheckFileType(ingestionId, FileTypeEnum.PDF, userVo);
        if (StringUtils.isEmpty(ingestion.getFilePath())) {
            throw new ApiException("未上传文件");
        }
        if (existsStructure(ingestionId)) {
            throw new ApiException("已解析结构");
        }
        if (CommonProgressStatusEnum.RUNNING.getId().equals(ingestion.getPdfToImageStatus())) {
            return;
        }
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getPdfToImageStatus, CommonProgressStatusEnum.RUNNING.getId())
                .update();
        pdfToImageExecutor.submit(() -> convertPdfToImages(ingestionId));
    }

    @Override
    public String getUploadImagesBatchNumber(int ingestionId, TokenUserVo userVo) {
        getIngestionCheckCanUpload(ingestionId, FileTypeEnum.IMAGE, userVo);
        return Long.toString(IdUtil.longId(), 36).toLowerCase();
    }

    @Override
    @SneakyThrows
    public int uploadImage(int ingestionId, String batchNumber, int index, MultipartFile file, TokenUserVo userVo) {
        if (!MultipartFileUtil.isImage(file)) {
            throw new ApiException("不是图片文件");
        }
        getIngestionCheckCanUpload(ingestionId, FileTypeEnum.IMAGE, userVo);

        // 上传至OSS
        String path = coachBookIngestionPathPrefix + ingestionId + "/upload/"
                + batchNumber + "/" + index + "-" + RandomStringUtils.randomAlphabetic(8).toLowerCase()
                + MultipartFileUtil.getFileTypeExtension(file);
        ossManager.uploadTK(path, file);

        Dimension dimension = ImageUtil.getImageDimensionFromMetaData(file.getInputStream());

        // 保存上传路径到redis
        PageImage pageImage = new PageImage(path, dimension.width, dimension.height, null);
        String redisKey = getImageUploadRedisKey(ingestionId, batchNumber);
        redisTemplate.opsForHash().put(redisKey, String.valueOf(index), JSON.toJSONString(pageImage));
        redisTemplate.expire(redisKey, 1, TimeUnit.HOURS);

        // 返回本批次已上传图片数
        return redisTemplate.opsForHash().size(redisKey).intValue();
    }

    private String getImageUploadRedisKey(int ingestionId, String batchNumber) {
        return "coach_book_ingestion:" + ingestionId + ":image_upload:" + batchNumber;
    }

    @Override
    public List<String> uploadImageComplete(int ingestionId, String batchNumber, TokenUserVo userVo) {
        CoachBookIngestion ingestion = getIngestionCheckCanUpload(ingestionId, FileTypeEnum.IMAGE, userVo);

        // 获取redis中保存的本批次图片路径
        String redisKey = getImageUploadRedisKey(ingestionId, batchNumber);
        Map<Object, Object> pathMap = redisTemplate.opsForHash().entries(redisKey);
        if (pathMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 按图片序号排序
        List<PageImage> batchPageImages = pathMap.entrySet().stream().sorted(Comparator.comparingInt(x -> Integer.parseInt((String) x.getKey())))
                .map(e -> (String) e.getValue()).map(v -> JSON.parseObject(v, PageImage.class)).collect(Collectors.toList());

        // 更新数据库
        List<PageImage> pageImages = coachBookIngestionService.parsePageImages(ingestion.getPageImages(), false);
        pageImages.addAll(batchPageImages);
        String filePath = pageImages.stream().map(PageImage::getUrl).collect(Collectors.joining(","));
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getFilePath, filePath)
                .set(CoachBookIngestion::getPageImages, JSON.toJSONString(pageImages))
                .set(CoachBookIngestion::getUploadBy, userVo.getUserId())
                .set(CoachBookIngestion::getUploadTime, new Date())
                .update();

        // 删除redis中的记录
        redisTemplate.delete(redisKey);

        return coachBookIngestionService.splitFilePathToPublicPath(filePath);
    }

    @Override
    public void changeImages(int ingestionId, List<String> filePath, TokenUserVo userVo) {
        if (new HashSet<>(filePath).size() != filePath.size()) {
            throw new ApiException("图片重复");
        }
        filePath = filePath.stream().map(ossManager::getObjectNameTK).collect(Collectors.toList());

        CoachBookIngestion ingestion = getIngestionCheckFileType(ingestionId, FileTypeEnum.IMAGE, userVo);
        Map<String, PageImage> pageImageMap = coachBookIngestionService.parsePageImages(ingestion.getPageImages(), false)
                .stream().collect(Collectors.toMap(PageImage::getUrl, Function.identity()));
        if (pageImageMap.isEmpty()) {
            throw new ApiException("未上传图片");
        }

        // 新图片列表，保持顺序
        List<PageImage> newPageImages = new ArrayList<>();
        for (String path : filePath) {
            PageImage pageImage = pageImageMap.remove(path);
            if (pageImage == null) {
                throw new ApiException("图片不存在");
            }
            newPageImages.add(pageImage);
        }

        // 检查删除的图片是否有关联结构
        if (!pageImageMap.isEmpty()) {
            Set<String> deleteImageSet = pageImageMap.keySet();
            List<CoachBookIngestionStructure> structures = coachBookIngestionStructureMapper.selectList(new LambdaQueryWrapper<CoachBookIngestionStructure>()
                    .eq(CoachBookIngestionStructure::getIngestionId, ingestionId));
            for (CoachBookIngestionStructure structure : structures) {
                List<String> imageUrlList = structure.getPageImageUrlList();
                for (String url : imageUrlList) {
                    if (deleteImageSet.contains(url)) {
                        throw new ApiException("页面图片已划分结构，不允许删除");
                    }
                }
            }
        }

        String newFilePath = newPageImages.stream().map(PageImage::getUrl).collect(Collectors.joining(","));
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getFilePath, newFilePath)
                .set(CoachBookIngestion::getPageImages, JSON.toJSONString(newPageImages))
                .update();
    }

    @Override
    public void changePageColumns(int ingestionId, List<PageImage> pageImages, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);

        List<PageImage> dbPageImages = coachBookIngestionService.parsePageImages(ingestion.getPageImages(), false);
        Map<String, PageImage> dbPageImageMap = dbPageImages.stream().collect(Collectors.toMap(PageImage::getUrl, Function.identity()));

        for (PageImage pageImage : pageImages) {
            String url = ossManager.getObjectNameTK(pageImage.getUrl());
            PageImage dbPageImage = dbPageImageMap.get(url);
            if (dbPageImage == null) {
                throw new ApiException("页面图片不存在");
            }
            dbPageImage.setColumnDividers(pageImage.getColumnDividers());
        }

        String updatedPageImages = JSON.toJSONString(dbPageImages);
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getPageImages, updatedPageImages)
                .update();
    }

    @Override
    public void deleteUploadedFile(int ingestionId, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);
        if (StringUtils.isEmpty(ingestion.getFilePath())) {
            return;
        }
        if (FileTypeEnum.PDF.getId().equals(ingestion.getFileType())) {
            if (CommonProgressStatusEnum.RUNNING.getId().equals(ingestion.getPdfToImageStatus())) {
                throw new ApiException("pdf文件正在转为图片，此时不允许删除");
            }
        }
        if (existsStructure(ingestionId)) {
            throw new ApiException("已解析结构，不允许删除");
        }
        log.info("删除教辅录入已上传文件, ingestionId = {}, userId = {}, \nfilePath = {}, \npageImages = {}", ingestionId, userVo.getUserId(), ingestion.getFilePath(), ingestion.getPageImages());
        coachBookIngestionService.lambdaUpdate().eq(CoachBookIngestion::getId, ingestionId)
                .set(CoachBookIngestion::getFilePath, null)
                .set(CoachBookIngestion::getPageImages, null)
                .set(CoachBookIngestion::getPdfPageCount, null)
                .set(CoachBookIngestion::getPdfToImageStatus, null)
                .set(CoachBookIngestion::getUploadBy, null)
                .set(CoachBookIngestion::getUploadTime, null)
                .update();
    }
}
