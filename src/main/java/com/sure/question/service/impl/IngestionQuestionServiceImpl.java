package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.IngestionPaper;
import com.sure.question.entity.IngestionQuestion;
import com.sure.question.entity.Question;
import com.sure.question.mapper.IngestionPaperMapper;
import com.sure.question.mapper.IngestionQuestionMapper;
import com.sure.question.mapper.QuestionMapper;
import com.sure.question.service.CoachBookIngestionService;
import com.sure.question.service.IngestionQuestionService;
import com.sure.question.vo.TokenUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class IngestionQuestionServiceImpl extends ServiceImpl<IngestionQuestionMapper, IngestionQuestion> implements IngestionQuestionService {
    private final CoachBookIngestionService coachBookIngestionService;
    private final IngestionPaperMapper ingestionPaperMapper;
    private final QuestionMapper questionMapper;

    /**
     * 获取题目
     */
    @Override
    public List<IngestionQuestion> getQuestions(int ingestionId, long paperId, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckPermission(ingestionId, userVo);
        return this.lambdaQuery()
                .eq(IngestionQuestion::getIngestionId, ingestionId)
                .eq(IngestionQuestion::getPaperId, paperId)
                .list();
    }

    /**
     * 添加题目
     */
    @Override
    public void addQuestions(int ingestionId, long paperId, List<IngestionQuestion> questions, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);
        int paperCount = ingestionPaperMapper.selectCount(new LambdaQueryWrapper<IngestionPaper>()
                .eq(IngestionPaper::getIngestionId, ingestionId)
                .eq(IngestionPaper::getPaperId, paperId));
        if (paperCount == 0) {
            throw new ApiException("试卷尚不存在，请先创建试卷");
        }
        checkQuestions(ingestionId, paperId, questions);
        this.saveBatch(questions);
    }

    private void checkQuestions(int ingestionId, long paperId, List<IngestionQuestion> questions) {
        for (IngestionQuestion q : questions) {
            q.setIngestionId(ingestionId);
            q.setPaperId(paperId);
            if (q.getQuestionId() == null) {
                throw new ApiException("题目Id不能为空");
            }
            if (StringUtils.isEmpty(q.getMainBodyAreas()) || StringUtils.equals(q.getMainBodyAreas(), "[]")) {
                throw new ApiException("题目正文区域不能为空");
            }
            if (StringUtils.isEmpty(q.getMainBodyContent())) {
                throw new ApiException("题目正文内容不能为空");
            }
        }
    }

    /**
     * 修改题目
     */
    @Override
    public void changeQuestions(int ingestionId, long paperId,List<IngestionQuestion> questions, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);
        Set<Long> dbQuestionIds = this.lambdaQuery()
                .eq(IngestionQuestion::getIngestionId, ingestionId)
                .eq(IngestionQuestion::getPaperId, paperId)
                .select(IngestionQuestion::getQuestionId)
                .list().stream().map(IngestionQuestion::getQuestionId).collect(Collectors.toSet());
        for (IngestionQuestion q : questions) {
            if (!dbQuestionIds.contains(q.getQuestionId())) {
                throw new ApiException("题目不存在");
            }
        }
        checkQuestions(ingestionId, paperId, questions);
        this.updateBatchById(questions);
    }

    /**
     * 删除题目
     */
    @Override
    public void deleteQuestions(int ingestionId, long paperId, List<Long> questionIds, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);
        int count = this.lambdaQuery().eq(IngestionQuestion::getIngestionId, ingestionId)
                .eq(IngestionQuestion::getPaperId, paperId)
                .in(IngestionQuestion::getQuestionId, questionIds)
                .count();
        if (count != questionIds.size()) {
            throw new ApiException("题目不存在");
        }

        int savedQuestionCount = questionMapper.selectCount(new LambdaQueryWrapper<Question>()
                .in(Question::getId, questionIds));
        if (savedQuestionCount > 0) {
            throw new ApiException("题目已保存，不允许删除");
        }

        this.lambdaUpdate()
                .eq(IngestionQuestion::getIngestionId, ingestionId)
                .in(IngestionQuestion::getQuestionId, questionIds)
                .remove();
    }
}