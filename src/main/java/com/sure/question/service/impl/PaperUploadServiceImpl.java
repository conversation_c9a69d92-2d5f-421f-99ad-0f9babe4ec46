package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.paper.structure.CheckPaperStructureOption;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.dto.question.SelectQuestionVoOption;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.PaperMain;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperEditStatusEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.enums.PaperUploadTypeEnum;
import com.sure.question.feign.MarkService;
import com.sure.question.mapper.CoachBookMapper;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.service.*;
import com.sure.question.util.IdUtil;
import com.sure.question.util.MultipartFileUtil;
import com.sure.question.util.WordConvertUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.paper.upload.DividePaperContentVo;
import com.sure.question.vo.paper.upload.UploadPaperParam;
import com.sure.question.vo.question.QuestionVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaperUploadServiceImpl implements PaperUploadService {
    private final PaperMainMapper paperMainMapper;
    private final PaperMainService paperMainService;
    private final PaperService paperService;
    private final QuestionBuildService questionBuildService;
    private final QuestionService questionService;
    private final PaperQuestionService paperQuestionService;
    private final CoachBookMapper coachBookMapper;
    private final CoachBookService coachBookService;
    private final MarkService markService;
    private final OssManager ossManager;
    private final OCRService ocrService;
    private final FeignService feignService;
    private final PermissionService permissionService;

    @Value("${aliyun.ossTkImg.prefix.paperHtml}")
    private String paperHtmlOssPathPrefix;
    @Value("${tempPath.uploadPaper}")
    private String uploadPaperTempPath;
    @Value("${aliyun.ossTkImg.prefix.uploadPaper}")
    private String uploadPaperOssPathPrefix;

    @Override
    public IPage<PaperMain> getSelfUpload(String userId, FindPaperPageParam param) {
        // 构建查询条件
        LambdaQueryWrapper<PaperMain> queryWrapper = param.buildPaperQueryWrapper()
                .eq(PaperMain::getUserId, userId)
                .eq(PaperMain::getUploadType, PaperUploadTypeEnum.UPLOAD.getId())
                .orderByDesc(PaperMain::getCreateTime);

        IPage<PaperMain> paperPage = paperMainMapper.selectPage(new Page<>(param.getPage(), param.getSize()), queryWrapper);

        // 补充教辅名称
        if (!paperPage.getRecords().isEmpty()) {
            List<Integer> coachBookIds = paperPage.getRecords().stream().map(PaperMain::getFlagCoachBookId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!coachBookIds.isEmpty()) {
                Map<Integer, String> coachBookIdCoachBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                                .in(CoachBook::getId, coachBookIds)
                                .select(CoachBook::getId, CoachBook::getBookName))
                        .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));
                paperPage.getRecords().forEach(x -> {
                    if (x.getFlagCoachBookId() != null) {
                        x.setFlagCoachBookName(coachBookIdCoachBookNameMap.get(x.getFlagCoachBookId()));
                    }
                });
            }
        }
        return paperPage;
    }

    @Override
    public String uploadPaper(MultipartFile file, UploadPaperParam param, boolean enablePdf, TokenUserVo userInfo) {
        // 1. 检查文件
        boolean isPdf = false;
        if (!MultipartFileUtil.isWord(file)) {
            if (!enablePdf || !MultipartFileUtil.isPdf(file)) {
                throw new ApiException("上传的文件格式错误");
            }
            isPdf = true;
        }
        String paperName = StringUtils.isNotBlank(param.getPaperName()) ? param.getPaperName() : MultipartFileUtil.getName(file);
        if (StringUtils.isEmpty(paperName)) {
            throw new ApiException("上传的文件没有名称");
        }

        // 2. 创建新试卷Id
        String paperId = IdUtil.StringId();

        // 3. 上传原文件到oss
        String uploadObjectName = uploadPaperOssPathPrefix + paperId + "-" + RandomStringUtils.randomAlphabetic(8).toLowerCase() + MultipartFileUtil.getOriginalExtension(file);
        ossManager.uploadTK(uploadObjectName, file);

        // 4. 内容转为html
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new ApiException("无法打开上传的文件");
        }
        String html;
        try {
            if (isPdf) {
                html = ocrService.convertPdfToHtml(inputStream, uploadPaperTempPath, 20, param.getGradeLevel(), param.getSubjectId());
            } else {
                html = WordConvertUtil.word2html(inputStream);
            }
        } catch (Exception ex) {
            if (isPdf) {
                log.error("pdf转html失败: \n", ex);
            } else {
                log.error("word转html失败: \n", ex);
            }
            if (ex instanceof ApiException) {
                throw new ApiException(ex.getMessage());
            }
            throw new ApiException("转换文档内容失败");
        }

        // 5. 移除图片水印
        html = paperMainService.removeAndAddHtmlImageWaterMark(html);

        // 6. 上传html到OSS
        String htmlObjectName = paperHtmlOssPathPrefix + paperId + "-" + RandomStringUtils.randomAlphabetic(8).toLowerCase() + ".html";
        ossManager.uploadTK(htmlObjectName, html.getBytes(StandardCharsets.UTF_8));

        // 7. 创建记录并存库
        PaperMain newPaper = createNewPaper(paperId, paperName, param, uploadObjectName, htmlObjectName, userInfo);
        paperMainMapper.insert(newPaper);

        return newPaper.getId();
    }

    private PaperMain createNewPaper(String paperId, String paperName, UploadPaperParam param, String uploadObjectName, String htmlObjectName, TokenUserVo userInfo) {
        PaperMain newPaper = new PaperMain();
        newPaper.setId(paperId);
        newPaper.setPaperName(paperName);
        newPaper.setGradeLevel(param.getGradeLevel());
        newPaper.setSubjectId(param.getSubjectId());
        newPaper.setGradeId(param.getGradeId());
        newPaper.setTerm(param.getTerm() == null ? null : param.getTerm().toString());
        newPaper.setYear(param.getYear());
        newPaper.setRegionId(param.getRegionId());
        newPaper.setPaperTypeId(param.getPaperTypeId());
        newPaper.setFlagCoachBookId(param.getFlagCoachBookId());
        newPaper.setPaperGroupId(param.getPaperGroupId());
        newPaper.setPaperBank(PaperBankEnum.Personal.getCode().toString());
        newPaper.setUploadType(PaperUploadTypeEnum.UPLOAD.getId().toString());
        newPaper.setPaperWay("0");
        newPaper.setStatus(PaperStatusEnum.Status0.getCode().toString());
        newPaper.setCheckStatus(PaperStatusEnum.CheckStatus0.getCode().toString());
        newPaper.setEditStatus(PaperStatusEnum.EditStatus0.getCode().toString());
        newPaper.setShowStatus("0");
        newPaper.setShareStatus(PaperStatusEnum.ShareStatus0.getCode().toString());
        newPaper.setGroupShareStatus(PaperStatusEnum.ShareStatus0.getCode());
        newPaper.setUserId(userInfo.getUserId());
        newPaper.setSchoolId(userInfo.getSchoolId());
        newPaper.setUploadFile(uploadObjectName);
        newPaper.setPaperLocation(htmlObjectName);
        newPaper.setBrowseCount(0);
        newPaper.setCreateTime(System.currentTimeMillis());
        newPaper.setUpdateTime(newPaper.getCreateTime());
        return newPaper;
    }

    /**
     * 划题获取试卷内容
     */
    @Override
    public DividePaperContentVo getDividePaperContent(String paperId, boolean addRawHtml, String userId) {
        PaperMain paperMain = getUploadPaperCheckPermission(paperId, userId);

        DividePaperContentVo result = new DividePaperContentVo();
        result.setInfo(paperMain);
        result.setStructureJson(paperMain.getPaperStyleJson());
        result.setQuestions(questionBuildService.selectPaperQuestionVos(paperId, SelectQuestionVoOption.addAll()));
        if (addRawHtml) {
            result.setRawHtml(getUploadPaperRawHtml(paperMain));
        }

        paperMain.setUserId(null);
        paperMain.setSchoolId(null);
        paperMain.setUploadFile(null);
        paperMain.setPaperLocation(null);
        paperMain.setPaperStyleJson(null);

        return result;
    }

    private String getUploadPaperRawHtml(PaperMain paperMain) {
        checkPaperLocation(paperMain);
        String path = paperMain.getPaperLocation();
        try (InputStream inputStream = ossManager.downloadTK(path)) {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException ex) {
            log.error("无法下载试卷Html文件, paperId = {}, htmlPath = {}\n", paperMain.getId(), path, ex);
            throw new ApiException(ex.getMessage());
        }
    }

    private void checkPaperLocation(PaperMain paperMain) {
        String path = paperMain.getPaperLocation();
        if (StringUtils.isEmpty(path)) {
            log.error("无试卷html路径, paperId = {}", paperMain.getId());
            throw new ApiException("找不到上传试卷内容");
        }
        // 不是oss路径则报错
        if (!path.startsWith(paperHtmlOssPathPrefix)) {
            log.error("请求试卷html本地文件, paperId = {}, htmlPath = {}", paperMain.getId(), path);
            throw new ApiException("上传文件已过期");
        }
    }

    /**
     * 查出试卷并检查权限
     */
    private PaperMain getUploadPaperCheckPermission(String paperId, String userId) {
        PaperMain paper = paperMainMapper.selectById(paperId);
        if (paper == null) {
            throw new ApiException("参数错误：找不到试卷");
        }
        // 本人上传，或对应学段学科编辑
        if (!StringUtils.equals(userId, paper.getUserId())) {
            permissionService.checkEditorPermission(userId, paper.getGradeLevel(), paper.getSubjectId());
        }
        if (!PaperUploadTypeEnum.UPLOAD.getId().toString().equals(paper.getUploadType())) {
            throw new ApiException("非上传划题试卷");
        }
        return paper;
    }

    /**
     * 查出试卷并检查权限，且划题未完成
     */
    private PaperMain getUploadPaperCheckPermissionAndEditable(String paperId, String userId) {
        PaperMain paper = getUploadPaperCheckPermission(paperId, userId);
        if (!PaperBankEnum.Personal.getCode().toString().equals(paper.getPaperBank())) {
            throw new ApiException("非个人库试卷");
        }
        if (!PaperEditStatusEnum.UNFINISHED.getId().toString().equals(paper.getEditStatus())) {
            throw new ApiException("试卷划题已完成");
        }
        return paper;
    }

    /**
     * 划题添加题目
     */
    @Override
    public List<QuestionVo> addDividePaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, String userId) {
        PaperMain paper = getUploadPaperCheckPermissionAndEditable(paperId, userId);
        questionService.addPaperQuestions(questionVos, CheckQuestionOption.skipAll(), paper, paperStructure, CheckPaperStructureOption.allowScoreNull());
        return questionBuildService.selectJustSavedQuestionVos(questionVos, SelectQuestionVoOption.addAll());
    }

    /**
     * 划题修改题目
     */
    @Override
    public List<QuestionVo> changeDividePaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, String userId) {
        getUploadPaperCheckPermissionAndEditable(paperId, userId);
        questionService.changePaperQuestions(questionVos, CheckQuestionOption.skipAll(), true, paperId, paperStructure, CheckPaperStructureOption.allowScoreNull());
        return questionBuildService.selectJustSavedQuestionVos(questionVos, SelectQuestionVoOption.addAll());
    }

    /**
     * 划题删除题目
     */
    @Override
    public void deleteDividePaperQuestions(String paperId, String paperStructure, List<String> questionIds, String userId) {
        getUploadPaperCheckPermissionAndEditable(paperId, userId);
        questionService.deletePaperQuestions(questionIds, paperId, paperStructure, CheckPaperStructureOption.allowScoreNull());
    }

    /**
     * 划题修改试卷结构
     */
    @Override
    public void changeDividePaperStructure(String paperId, String paperStructure, String userId) {
        getUploadPaperCheckPermissionAndEditable(paperId, userId);
        paperQuestionService.checkPaperStructure(paperId, paperStructure, CheckPaperStructureOption.allowScoreNull(), null, null, null);
        paperMainMapper.updatePaperStructure(paperId, paperStructure);
    }

    /**
     * 完成划题
     */
    @Override
    public void finishDivide(String paperId, boolean allowNoScore, TokenUserVo userVo) {
        PaperMain paper = getUploadPaperCheckPermissionAndEditable(paperId, userVo.getUserId());
        // 仅题库编辑允许不设置分数
        boolean isEditor = permissionService.hasEditorPermission(userVo, paper.getGradeLevel(), paper.getSubjectId());
        if (!isEditor) {
            allowNoScore = false;
        }
        paperService.checkPaperAndQuestionsFinished(paperId, allowNoScore);
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set(PaperMain::getEditStatus, PaperEditStatusEnum.FINISHED.getId().toString()));
    }

    /**
     * 重启划题
     */
    @Override
    public void restartDivide(String paperId, String userId) {
        PaperMain paper = getUploadPaperCheckPermission(paperId, userId);
        if (!PaperBankEnum.Personal.getCode().toString().equals(paper.getPaperBank())) {
            throw new ApiException("非个人库试卷");
        }
        if (!PaperEditStatusEnum.FINISHED.getId().toString().equals(paper.getEditStatus())) {
            throw new ApiException("试卷划题尚未完成");
        }
        if (coachBookService.isCoachBookPaperAndLocked(paper.getId())) {
            throw new ApiException("教辅试卷已锁定");
        }
        if (!feignService.getPaperIdAnswerSheetMap(Collections.singletonList(paperId)).isEmpty()) {
            throw new ApiException("已生成答题卡");
        }
        if (!feignService.getPaperIdFeedbackSheetMap(Collections.singletonList(paperId)).isEmpty()) {
            throw new ApiException("已生成错题反馈卡");
        }
        // 检查是否绑定到考试科目
        Result response = markService.checkPaperExamSubjectBinding(paperId);
        if (!Objects.equals(response.getCode(), StatusCode.OK)) {
            throw new ApiException(response.getMessage());
        }

        try {
            checkPaperLocation(paper);
        } catch (Exception e) {
            throw new ApiException("上传文件已过期");
        }

        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set(PaperMain::getEditStatus, PaperEditStatusEnum.UNFINISHED.getId().toString()));
    }
}
