package com.sure.question.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.paper.structure.CheckPaperStructureOption;
import com.sure.question.dto.paper.structure.PaperStructure;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.entity.*;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.feign.UserService;
import com.sure.question.mapper.*;
import com.sure.question.service.*;
import com.sure.question.util.WordConvertUtil;
import com.sure.question.vo.Basket;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.CoachBookPaperMainVo;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.question.QuestionVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaperServiceImpl implements PaperService {
    private final PaperMainMapper paperMainMapper;
    private final PaperQuestionMapper paperQuestionMapper;
    private final FavoritePaperMapper favoritePaperMapper;
    private final QuestionBuildService questionBuildService;
    private final CoachBookPaperMapper coachBookPaperMapper;
    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final SchoolCoachBookMapper schoolCoachBookMapper;
    private final PaperStuAccessMapper paperStuAccessMapper;
    private final CoachStuCardMapper coachStuCardMapper;
    private final CoachBookService coachBookService;
    private final QuestionEsSyncMapper questionEsSyncMapper;
    private final BasketPaperStyleMapper basketPaperStyleMapper;
    private final PaperQuestionService paperQuestionService;
    private final RenderedQuestionService renderedQuestionService;
    private final PermissionService permissionService;
    private final UserService userService;


    /**
     * 公共库试卷查询
     */
    @Override
    public IPage<PaperMain> findInPublicBank(FindPaperPageParam param, String userId) {
        LambdaQueryWrapper<PaperMain> lambdaQueryWrapper = param.buildPaperQueryWrapper()
                // 公共库
                .eq(PaperMain::getPaperBank, PaperBankEnum.Public.getCode());
        if (StringUtils.equalsIgnoreCase(param.getOrderBy(), "year")) {
            lambdaQueryWrapper.orderByDesc(PaperMain::getYear);
        } else if (StringUtils.equalsIgnoreCase(param.getOrderBy(), "browseCount")) {
            lambdaQueryWrapper.orderByDesc(PaperMain::getBrowseCount);
        }
        lambdaQueryWrapper.orderByDesc(PaperMain::getId);


        IPage<PaperMain> paperPage = paperMainMapper.selectPage(new Page<>(param.getPage(), param.getSize()), lambdaQueryWrapper);

        if (!paperPage.getRecords().isEmpty()) {
            // 标记是否被收藏
            List<String> paperIds = paperPage.getRecords().stream().map(PaperMain::getId).collect(Collectors.toList());
            List<String> favoritePaperIds = favoritePaperMapper.selectPaperIdListByUserIdAndPaperIds(paperIds, userId);
            paperPage.getRecords().forEach(p -> p.setIfFavorite(favoritePaperIds.contains(p.getId())));
            // 清除试卷结构
            paperPage.getRecords().forEach(p -> p.setPaperStyleJson(null));
        }

        return paperPage;
    }

    /**
     * 收藏试卷查询
     */
    @Override
    public IPage<PaperMain> getFavorite(FindPaperPageParam param, String userId) {
        return paperMainMapper.selectFavoritePaperPage(new Page<>(param.getPage(), param.getSize()), userId,
                param.getStage(), param.getSubject(), param.getType(), param.getGradeId(), param.getTerm());
    }

    /**
     * 改变试卷收藏状态
     */
    @Override
    public void changeFav(Long paperId, String userId, String schoolId) {
        LambdaQueryWrapper<FavoritePaper> queryWrapper = new LambdaQueryWrapper<FavoritePaper>()
                .eq(FavoritePaper::getPaperId, paperId)
                .eq(FavoritePaper::getUserId, userId);
        // 查出记录
        List<FavoritePaper> favoritePapers = favoritePaperMapper.selectList(queryWrapper);
        // 存在收藏则删除收藏
        if (!favoritePapers.isEmpty()) {
            favoritePaperMapper.delete(queryWrapper);
        }
        // 尚未收藏则添加收藏
        else {
            FavoritePaper favoritePaper = new FavoritePaper();
            favoritePaper.setUserId(userId);
            favoritePaper.setPaperId(paperId);
            favoritePaper.setSchoolId(schoolId);
            favoritePaper.setCreateTime(System.currentTimeMillis());
            favoritePaperMapper.insert(favoritePaper);
        }
    }

    /**
     * 获取试卷内容
     */
    @Override
    public Basket getPaperContent(String paperId) {
        Basket basket = new Basket();
        PaperMain paperMain = addBasketPaperInfo(basket, paperId);
        addBasketPaperStyle(basket, paperMain);
        basket.setPaperStructure(paperMain.getPaperStyleJson());
        basket.setQuestions(questionBuildService.selectPaperQuestionVos(paperId));
        return basket;
    }

    private PaperMain addBasketPaperInfo(Basket basket, String paperId) {
        PaperMain paperMain = paperMainMapper.selectById(paperId);
        if (paperMain == null) {
            throw new ApiException("试卷不存在");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("id", paperMain.getId());
        map.put("paperName", paperMain.getPaperName());
        map.put("gradeLevel", paperMain.getGradeLevel());
        map.put("subjectId", paperMain.getSubjectId());
        map.put("paperTypeId", paperMain.getPaperTypeId());
        map.put("answerSheetDownloadCount", paperMain.getAnswerSheetDownloadCount());
        map.put("feedbackSheetDownloadCount", paperMain.getFeedbackSheetDownloadCount());
        basket.setPaperInfo(map);
        return paperMain;
    }

    private void addBasketPaperStyle(Basket basket, PaperMain paperMain) {
        BasketPaperStyle basketPaperStyle = basketPaperStyleMapper.selectById(paperMain.getId());
        if (basketPaperStyle == null) {
            basketPaperStyle = new BasketPaperStyle();
            basketPaperStyle.setPaperId(paperMain.getId());
            basketPaperStyle.setQuestionCategoryJson(paperMain.getPaperStyleJson());
            basketPaperStyle.setPaperInfo(JSON.toJSONString(basket.getPaperInfo()));
            basketPaperStyle.setTitle(paperMain.getPaperName());
        } else {
            basketPaperStyle.setQuestionCategoryJson(paperMain.getPaperStyleJson());
        }
        basket.setBasketPaperStyle(basketPaperStyle);
    }

    /**
     * 获取试卷内容（已渲染公式）
     */
    @Override
    public Basket getPaperContentRendered(String paperId) {
        Basket paper = getPaperContent(paperId);
        renderedQuestionService.replaceRenderedContent(paper.getQuestions(), true);
        return paper;
    }

    /**
     * 获取教辅试卷内容
     */
    @Override
    public Basket getCoachBookPaperContent(String paperId, String schoolId) {
        Basket content = getPaperContent(paperId);
        // 查教辅试卷
        CoachBookPaper coachBookPaper = coachBookPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getPaperId, paperId)
                .select(CoachBookPaper::getCoachBookId, CoachBookPaper::getScoreAnalysis));
        // 补充是否启用分值分析
        if (coachBookPaper != null) {
            content.getPaperInfo().put("scoreAnalysis", coachBookPaper.getScoreAnalysis());
            // 查学校教辅
            SchoolCoachBook schoolCoachBook = schoolCoachBookMapper.selectOne(new LambdaQueryWrapper<SchoolCoachBook>()
                        .eq(SchoolCoachBook::getCoachBookId, coachBookPaper.getCoachBookId())
                        .eq(SchoolCoachBook::getSchoolId, schoolId));
            // 补充错题标记方式
            if (schoolCoachBook != null) {
                content.getPaperInfo().put("wrongQuesQuickMark", schoolCoachBook.getWrongQuesQuickMark());
                content.getPaperInfo().put("wrongQuesPaperMark", schoolCoachBook.getWrongQuesPaperMark());
            }
        }
        return content;
    }

    /**
     * 获取教辅试卷内容（已渲染公式）
     */
    @Override
    public Basket getCoachBookPaperContentRendered(String paperId, String schoolId) {
        Basket paper = getCoachBookPaperContent(paperId, schoolId);
        renderedQuestionService.replaceRenderedContent(paper.getQuestions(), true);
        return paper;
    }

    /**
     * 家长查教辅试卷内容
     */
    @Override
    public Basket getCoachBookPaperContentParent(String paperId, String studentId, String schoolId, TokenUserVo userVo) {
        Result res = userService.isChildOfParent(userVo.getUserId(), studentId, false, true, schoolId);
        if (!StatusCode.OK.equals(res.getCode())) {
            throw new ApiException(res.getMessage());
        }
        if (!Boolean.TRUE.equals(res.getData())) {
            throw new ApiException("家长小孩参数错误");
        }

        // 查教辅试卷或教辅练习卷
        Integer paperCoachBookId = coachBookPaperMapper.selectCoachBookIdByPaperId(paperId);
        List<Integer> customPaperCoachBookIds = coachBookCustomPaperMapper.selectCoachBookIdsByPaperId(paperId);
        if (paperCoachBookId == null && customPaperCoachBookIds.isEmpty()) {
            log.error("【查询学校教辅试卷】该试卷不属于定制资源！paperId={}, schoolId={}", paperId, schoolId);
            throw new ApiException("该试卷不属于定制资源");
        }
        // 确保学校定制该教辅
        List<Integer> coachBookIds = new ArrayList<>(customPaperCoachBookIds);
        if (paperCoachBookId != null) {
            coachBookIds.add(paperCoachBookId);
        }
        List<Integer> schoolCoachBookIds = schoolCoachBookMapper.querySchoolCoachBookIds(schoolId, coachBookIds);
        if (schoolCoachBookIds.isEmpty()) {
            log.error("【查询学校教辅试卷】本校尚未定制试卷所属教辅！paperId={}, schoolId={}, coachBookIds={}", paperId, schoolId, JSONUtil.toJsonStr(coachBookIds));
            throw new ApiException("本校尚未定制试卷所属教辅");
        }
        // 记录查看教辅练习卷
        customPaperCoachBookIds.removeIf(coachBookId -> !schoolCoachBookIds.contains(coachBookId));
        if (!customPaperCoachBookIds.isEmpty()) {
            List<Integer> coachBookIdList = coachStuCardMapper.selectList(new LambdaQueryWrapper<CoachStuCard>()
                            .eq(CoachStuCard::getStudentId, studentId)
                            .in(CoachStuCard::getCoachBookId, customPaperCoachBookIds)
                            .select(CoachStuCard::getCoachBookId)
                            .orderByDesc(CoachStuCard::getActiveTime))
                    .stream().map(CoachStuCard::getCoachBookId).distinct().collect(Collectors.toList());
            if (!coachBookIdList.isEmpty()) {
                paperStuAccessMapper.insertIgnore(studentId, userVo.getUserId(), paperId, coachBookIdList.get(0), 0);
            }
        }

        return getCoachBookPaperContent(paperId, schoolId);
    }

    /**
     * 查试卷名称
     */
    @Override
    public String getPaperNameByPaperId(String paperId) {
        PaperMain paperMain = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId).select(PaperMain::getPaperName));
        return paperMain != null ? paperMain.getPaperName() : null;
    }

    /**
     * 查试卷信息
     */
    @Override
    public PaperMain getPaperInfo(String paperId) {
        PaperMain paperMain = paperMainMapper.selectById(paperId);
        if (paperMain == null) {
            throw new ApiException("找不到试卷");
        }
        paperMain.setUserId(null);
        paperMain.setSchoolId(null);
        paperMain.setUploadFile(null);
        paperMain.setPaperLocation(null);
        return paperMain;
    }

    /**
     * 查教辅试卷信息
     */
    @Override
    public CoachBookPaperMainVo getCoachBookPaperInfo(String paperId, String schoolId) {
        PaperMain paperInfo = getPaperInfo(paperId);
        CoachBookPaperMainVo result = CoachBookPaperMainVo.createFromPaperMain(paperInfo);

        // 查教辅试卷
        CoachBookPaper coachBookPaper = coachBookPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getPaperId, paperId)
                .select(CoachBookPaper::getCoachBookId, CoachBookPaper::getScoreAnalysis));
        if (coachBookPaper != null) {
            // 补充是否启用分值分析
            result.setScoreAnalysis(coachBookPaper.getScoreAnalysis());
            // 查学校教辅
            SchoolCoachBook schoolCoachBook = schoolCoachBookMapper.selectOne(new LambdaQueryWrapper<SchoolCoachBook>()
                    .eq(SchoolCoachBook::getCoachBookId, coachBookPaper.getCoachBookId())
                    .eq(SchoolCoachBook::getSchoolId, schoolId));
            // 补充错题标记方式
            if (schoolCoachBook != null) {
                result.setWrongQuesQuickMark(schoolCoachBook.getWrongQuesQuickMark());
                result.setWrongQuesPaperMark(schoolCoachBook.getWrongQuesPaperMark());
            }
        }
        return result;
    }

    /**
     * 修改试卷信息
     */
    @Override
    public void changeInfo(PaperMain paperMain, TokenUserVo userVo) {
        String paperId = paperMain.getId();
        if (paperId == null) {
            throw new ApiException("参数错误");
        }
        PaperMain paper = paperMainMapper.selectById(paperId);
        if (paper == null) {
            throw new ApiException("查无此卷");
        }

        // 检查修改权限
        permissionService.checkUpdatePaperPermission(paper, userVo);

        // 修改试卷信息
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set(PaperMain::getPaperName, paperMain.getPaperName())
                .set(PaperMain::getGradeLevel, paperMain.getGradeLevel())
                .set(PaperMain::getGradeId, paperMain.getGradeId())
                .set(PaperMain::getTerm, paperMain.getTerm())
                .set(PaperMain::getSubjectId, paperMain.getSubjectId())
                .set(PaperMain::getRegionId, paperMain.getRegionId())
                .set(PaperMain::getFlagCoachBookId, paperMain.getFlagCoachBookId())
                .set(PaperMain::getPaperGroupId, paperMain.getPaperGroupId())
                .set(PaperMain::getYear, paperMain.getYear())
                .set(PaperMain::getPaperTypeId, paperMain.getPaperTypeId())
                .set(PaperMain::getUpdateTime, System.currentTimeMillis()));

        // 重新生成定制卷PDF
        coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getPaperId, paperId)
                .set(CoachBookCustomPaper::getPdfStatus, null)
                .set(CoachBookCustomPaper::getPdfPath, null));
        if (PaperBankEnum.Public.getCode().toString().equals(paper.getPaperBank())) {
            List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds(paperId);
            if (questionIds.isEmpty()) {
                questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
            }
        }
    }

    /**
     * 修改试卷结构
     */
    @Override
    public void changePaperStructure(String paperId, String structure, TokenUserVo userVo) {
        permissionService.checkUpdatePaperPermission(paperId, userVo);
        paperMainMapper.updatePaperStructure(paperId, structure);
        // 重新生成定制卷PDF
        coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getPaperId, paperId)
                .set(CoachBookCustomPaper::getPdfStatus, null));
    }

    /**
     * 修改试卷题目别名
     */
    @Override
    public void updateQuestionAlias(String paperId, String paperStructure, TokenUserVo userVo) {
        PaperMain paper = paperMainMapper.selectById(paperId);
        permissionService.checkUpdatePaperPermission(paper, userVo);

        if (coachBookService.isCoachBookPaperAndLocked(paper.getId())) {
            throw new ApiException("教辅试卷已锁定，禁止修改分数题名");
        }

        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set(PaperMain::getPaperStyleJson, paperStructure)
                .set(PaperMain::getUpdateTime, System.currentTimeMillis()));
        // 重新生成定制卷PDF
        coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getPaperId, paperId)
                .set(CoachBookCustomPaper::getPdfStatus, null));
    }

    /**
     * 增加答题卡或反馈卡下载次数
     */
    @Override
    public void addSheetDownloadCount(String paperId, String type) {
        checkSheetTypeThrowEx(type);

        PaperMain paperMain = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .select(PaperMain::getAnswerSheetDownloadCount, PaperMain::getFeedbackSheetDownloadCount));
        if (paperMain == null) {
            throw new ApiException("试卷不存在");
        }

        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set("answerSheet".equals(type), PaperMain::getAnswerSheetDownloadCount, paperMain.getAnswerSheetDownloadCount() + 1)
                .set("feedbackSheet".equals(type), PaperMain::getFeedbackSheetDownloadCount, paperMain.getFeedbackSheetDownloadCount() + 1));
    }

    private void checkSheetTypeThrowEx(String type) {
        if (!StringUtils.equalsAny(type, "answerSheet", "feedbackSheet")) {
            throw new ApiException("type参数错误");
        }
    }

    /**
     * 获取答题卡或反馈卡下载次数
     */
    @Override
    public Integer getSheetDownloadCount(String paperId, String type) {
        checkSheetTypeThrowEx(type);
        PaperMain paperMain = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .select(PaperMain::getId, PaperMain::getAnswerSheetDownloadCount, PaperMain::getFeedbackSheetDownloadCount));
        if (paperMain == null) {
            return null;
        }
        if ("answerSheet".equals(type)) {
            return paperMain.getAnswerSheetDownloadCount();
        } else {
            return paperMain.getFeedbackSheetDownloadCount();
        }
    }

    /**
     * 试卷浏览次数自增1
     */
    @Override
    public void paperViewIncr(String paperId) {
        paperMainMapper.paperViewIncr(paperId);
    }

    /**
     * 试卷下载次数自增1
     */
    @Override
    public void paperDownIncr(String paperId) {
        paperMainMapper.paperDownIncr(paperId);
    }

    /**
     * 删除试卷（标记删除）
     */
    @Override
    public void delPaperById(String paperId, TokenUserVo userVo) {
        PaperMain paperMain = paperMainMapper.selectById(paperId);
        if (paperMain == null) {
            return;
        }
        permissionService.checkUpdatePaperPermission(paperMain, userVo);

        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set(PaperMain::getStatus, PaperStatusEnum.Status1.getCode()));

        // 同步ES
        if (PaperBankEnum.Public.getCode().toString().equals(paperMain.getPaperBank())) {
            List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds(paperId);
            if (!questionIds.isEmpty()) {
                questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
            }
        }
    }

    /**
     * 下载试卷word
     */
    @Override
    public void downloadPaperWord(String paperId, String html, String size, Integer gradeId, Integer paperTypeId, TokenUserVo userVo, OutputStream outputStream) {
        PaperMain paperMain = paperMainMapper.selectById(paperId);
        if (paperMain == null) {
            throw new ApiException("找不到试卷");
        }
        permissionService.checkViewPaperContentPermission(paperMain, userVo);
        // 将html转换为word
        WordConvertUtil.html2word(html, size, outputStream);
        // 下载本人试卷word，可以更新试卷信息
        if (userVo.getUserId().equals(paperMain.getUserId()) && gradeId != null && paperTypeId != null) {
            paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                    .eq(PaperMain::getId, paperId)
                    .eq(PaperMain::getUserId, userVo.getUserId())
                    .set(PaperMain::getGradeId, gradeId)
                    .set(PaperMain::getPaperTypeId, paperTypeId));
        }
    }

    /**
     * 检查试卷及题目是否完整
     */
    @Override
    public void checkPaperAndQuestionsFinished(String paperId, boolean allowNoScore) {
        PaperMain paper = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .select(PaperMain::getPaperStyleJson));
        if (paper == null) {
            throw new ApiException("试卷不存在");
        }

        // 检查试卷结构
        try {
            CheckPaperStructureOption checkPaperStructureOption = allowNoScore ? CheckPaperStructureOption.allowScoreNull() : CheckPaperStructureOption.checkAll();
            paperQuestionService.checkPaperStructure(paperId, paper.getPaperStyleJson(), checkPaperStructureOption, null, null, null);
        } catch (ApiException e) {
            throw new ApiException("检查试卷结构不通过：" + e.getMessage());
        }

        // 检查题目
        PaperStructure structure = JSON.parseObject(paper.getPaperStyleJson(), PaperStructure.class);
        Map<String, String> questionIdNameMap = structure.extractQuestionIdNameMap();
        List<QuestionVo> questionVos = questionBuildService.selectPaperQuestionVos(paperId);
        StringBuilder sb = new StringBuilder();
        questionVos.forEach(questionVo -> {
            try {
                questionVo.check(CheckQuestionOption.checkAllExceptScore());
            } catch (ApiException e) {
                sb.append("\n").append(questionIdNameMap.get(questionVo.getId())).append("：").append(e.getMessage());
            }
        });
        if (sb.length() > 0) {
            throw new ApiException("检查题目不通过：\n" + sb);
        }
    }

    @Override
    public CoachBookPaperMainVo getSchoolCoachPaper(String id, String schoolId, String studentId, String userId) {
        // 可能的教辅ID列表
        List<Integer> coachBookIds = new ArrayList<>();
        // 查出教辅 id
        Integer bookId = coachBookPaperMapper.selectCoachBookIdByPaperId(id);
        if (bookId == null) {
            coachBookIds = coachBookCustomPaperMapper.selectCoachBookIdsByPaperId(id);
            if (coachBookIds.isEmpty()) {
                log.error("【查询学校教辅试卷】该试卷不属于定制资源！paperId={}, schoolId={}", id, schoolId);
                throw new ApiException("该试卷不属于定制资源");
            }
        } else {
            coachBookIds.add(bookId);
        }
        // 确保学校定制该教辅
        List<Integer> schoolCoachBookIds = schoolCoachBookMapper.querySchoolCoachBookIds(schoolId, coachBookIds);
        if (schoolCoachBookIds.isEmpty()) {
            log.error("【查询学校教辅试卷】本校尚未定制试卷所属教辅！paperId={}, schoolId={}, coachBookIds={}", id, schoolId, JSONUtil.toJsonStr(coachBookIds));
            throw new ApiException("本校尚未定制试卷所属教辅");
        }

        if (StringUtils.isNotBlank(studentId) && StringUtils.isNotBlank(userId)) {
            // 如果传学生ID则是家长查看试卷，需要记录学生查看试卷的记录
            if (schoolCoachBookIds.size() == 1) {
                bookId = schoolCoachBookIds.get(0);
            } else {
                List<Integer> coachBookIdList = coachStuCardMapper.selectList(new LambdaQueryWrapper<CoachStuCard>()
                                .eq(CoachStuCard::getStudentId, studentId)
                                .in(CoachStuCard::getCoachBookId, schoolCoachBookIds)
                                .select(CoachStuCard::getCoachBookId)
                                .orderByDesc(CoachStuCard::getActiveTime))
                        .stream().map(CoachStuCard::getCoachBookId).distinct().collect(Collectors.toList());
                bookId = coachBookIdList.get(0);
            }

            paperStuAccessMapper.insertIgnore(studentId, userId, id, bookId, 0);
        }

        PaperMain paperVo = getPaperInfo(id);
        CoachBookPaperMainVo result = CoachBookPaperMainVo.createFromPaperMain(paperVo);

        // 补充错题标记方式
        SchoolCoachBook schoolCoachBook = schoolCoachBookMapper.selectOne(new LambdaQueryWrapper<SchoolCoachBook>()
                .eq(SchoolCoachBook::getCoachBookId, bookId)
                .eq(SchoolCoachBook::getSchoolId, schoolId));
        result.setWrongQuesQuickMark(schoolCoachBook.getWrongQuesQuickMark());
        result.setWrongQuesPaperMark(schoolCoachBook.getWrongQuesPaperMark());

        // 补充是否启用分值分析
        if (bookId != null) {
            CoachBookPaper coachBookPaper = coachBookPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookPaper>()
                    .eq(CoachBookPaper::getCoachBookId, bookId)
                    .eq(CoachBookPaper::getPaperId, id));
            if (coachBookPaper != null) {
                result.setScoreAnalysis(coachBookPaper.getScoreAnalysis());
            }
        }

        return result;
    }

    @Override
    public Page<PaperMain> getAvailableCoachBookPapers(String userId, Integer page, Integer size, String keyword, Integer stage,
                                                       Integer subjectId, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId) {
        return lstAvailableCoachBookPapers(userId, 2, page, size, keyword, stage, subjectId, coachBookId, flagCoachBookId, paperGroupId);
    }

    private Page<PaperMain> lstAvailableCoachBookPapers(String userId, Integer uploadType, Integer page, Integer size, String keyword, Integer stage,
                                                        Integer subjectId, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId) {
        Page<PaperMain> p = new Page<>(page, size);
        p.setOrders(Collections.singletonList(new OrderItem().setColumn("create_time").setAsc(Boolean.FALSE)));
        Page<PaperMain> result = paperMainMapper.selectPage(p, new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getUserId, userId)
                .eq(PaperMain::getUploadType, uploadType)
                .eq(PaperMain::getStatus, 0)
                .eq(PaperMain::getSubjectId, subjectId)
                .eq(PaperMain::getGradeLevel, stage)
                .eq(flagCoachBookId != null, PaperMain::getFlagCoachBookId, flagCoachBookId)
                .eq(paperGroupId != null, PaperMain::getPaperGroupId, paperGroupId)
                .like(StringUtils.isNotEmpty(keyword), PaperMain::getPaperName, keyword)
                .select(PaperMain::getId, PaperMain::getPaperName, PaperMain::getGradeId, PaperMain::getFlagCoachBookId, PaperMain::getPaperGroupId, PaperMain::getCreateTime));

        if (result.getTotal() > 0) {
            // 一份卷只能属于一本教辅的同步卷，但可以是多本教辅的练习卷
            Set<String> paperIdSet = coachBookPaperMapper.selectObjs(new LambdaQueryWrapper<CoachBookPaper>().select(CoachBookPaper::getPaperId))
                    .stream().map(String::valueOf).collect(Collectors.toSet());
            Set<String> paperIds = coachBookCustomPaperMapper.selectObjs(new LambdaQueryWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getPaperId, coachBookId)
                    .select(CoachBookCustomPaper::getPaperId)).stream().map(String::valueOf).collect(Collectors.toSet());
            paperIdSet.addAll(paperIds);
            // 剔除同步卷、练习卷
            if (!paperIdSet.isEmpty()) {
                result.getRecords().removeIf(x -> paperIdSet.contains(x.getId()));
            }
        }

        return result;
    }

    @Override
    public Page<PaperMain> getAvailableCoachBookPapersUpload(String userId, Integer page, Integer size, String keyword, Integer stage, Integer subjectId, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId) {
        return lstAvailableCoachBookPapers(userId, 0, page, size, keyword, stage, subjectId, coachBookId, flagCoachBookId, paperGroupId);
    }
}

