package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.CoachBookIngestion;
import com.sure.question.enums.FileTypeEnum;
import com.sure.question.mapper.CoachBookIngestionMapper;
import com.sure.question.mapper.CoachBookMapper;
import com.sure.question.service.CoachBookIngestionService;
import com.sure.question.service.OssManager;
import com.sure.question.service.PermissionService;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.IngestionVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CoachBookIngestionServiceImpl extends ServiceImpl<CoachBookIngestionMapper, CoachBookIngestion> implements CoachBookIngestionService {
    private final PermissionService permissionService;
    private final CoachBookMapper coachBookMapper;
    private final OssManager ossManager;

    @Override
    public IngestionVo getByCoachBookId(int coachBookId, TokenUserVo tokenUserVo) {
        getCoachBookCheckPermission(coachBookId, tokenUserVo);
        CoachBookIngestion ingestion = lambdaQuery()
                .eq(CoachBookIngestion::getCoachBookId, coachBookId)
                .eq(CoachBookIngestion::getDeleted, false)
                .one();
        return ingestion == null ? null : buildIngestionVo(ingestion);
    }

    private CoachBook getCoachBookCheckPermission(int coachBookId, TokenUserVo userVo) {
        CoachBook coachBook = coachBookMapper.selectById(coachBookId);
        if (coachBook == null) {
            throw new ApiException("找不到教辅");
        }
        permissionService.checkEditorPermission(userVo, coachBook.getGradeLevel(), coachBook.getSubjectId());
        return coachBook;
    }

    private IngestionVo buildIngestionVo(CoachBookIngestion ingestion) {
        return IngestionVo.builder()
                .id(ingestion.getId())
                .coachBookId(ingestion.getCoachBookId())
                .gradeLevel(ingestion.getGradeLevel())
                .subjectId(ingestion.getSubjectId())
                .fileType(ingestion.getFileType())
                .filePath(splitFilePathToPublicPath(ingestion.getFilePath()))
                .pageImages(parsePageImages(ingestion.getPageImages(), true))
                .pdfPageCount(ingestion.getPdfPageCount())
                .pdfToImageStatus(ingestion.getPdfToImageStatus())
                .build();
    }

    @Override
    public List<String> splitFilePathToPublicPath(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            return new ArrayList<>();
        }
        return Arrays.stream(filePath.split(",")).filter(StringUtils::isNotEmpty)
                .map(ossManager::getPublicPathTK).collect(Collectors.toList());
    }

    @Override
    public List<PageImage> parsePageImages(String pageImages, boolean toPublicPath) {
        if (StringUtils.isEmpty(pageImages)) {
            return new ArrayList<>();
        }
        List<PageImage> result = JSON.parseArray(pageImages, PageImage.class);
        if (toPublicPath) {
            result.forEach(x -> x.setUrl(ossManager.getPublicPathTK(x.getUrl())));
        }
        return result;
    }

    @Override
    public IngestionVo create(int coachBookId, String fileType, TokenUserVo userVo) {
        if (!FileTypeEnum.PDF.getId().equals(fileType) && !FileTypeEnum.IMAGE.getId().equals(fileType)) {
            throw new ApiException("上传文件类型错误，仅支持pdf和图片");
        }
        CoachBook coachBook = getCoachBookCheckPermission(coachBookId, userVo);

        CoachBookIngestion existsIngestion = lambdaQuery()
                .eq(CoachBookIngestion::getCoachBookId, coachBookId)
                .eq(CoachBookIngestion::getDeleted, false)
                .one();
        if (existsIngestion != null) {
            throw new ApiException("录入项目已存在");
        }

        CoachBookIngestion ingestion = CoachBookIngestion.builder()
                .coachBookId(coachBookId)
                .gradeLevel(coachBook.getGradeLevel())
                .subjectId(coachBook.getSubjectId())
                .fileType(fileType)
                .createBy(userVo.getUserId())
                .createTime(new Date())
                .finished(false)
                .deleted(false)
                .build();
        baseMapper.insert(ingestion);
        return buildIngestionVo(ingestion);
    }

    @Override
    public CoachBookIngestion getIngestionCheckPermission(int ingestionId, TokenUserVo userVo) {
        CoachBookIngestion ingestion = baseMapper.selectById(ingestionId);
        if (ingestion == null) {
            throw new ApiException("找不到录入记录");
        }
        permissionService.checkEditorPermission(userVo, ingestion.getGradeLevel(), ingestion.getSubjectId());
        return ingestion;
    }

    @Override
    public CoachBookIngestion getIngestionCheckNotFinishedNotDeleted(int ingestionId, TokenUserVo userVo) {
        CoachBookIngestion ingestion = getIngestionCheckPermission(ingestionId, userVo);
        if (ingestion.getFinished()) {
            throw new ApiException("录入已完成");
        }
        if (ingestion.getDeleted()) {
            throw new ApiException("该录入项目已删除");
        }
        return ingestion;
    }
}




