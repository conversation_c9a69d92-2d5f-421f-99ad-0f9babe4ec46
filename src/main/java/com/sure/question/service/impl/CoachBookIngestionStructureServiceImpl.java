package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.dto.llm.RequestParam;
import com.sure.question.entity.CoachBookIngestion;
import com.sure.question.entity.CoachBookIngestionStructure;
import com.sure.question.entity.CoachBookIngestionTocLlm;
import com.sure.question.entity.IngestionPaper;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.CommonProgressStatusEnum;
import com.sure.question.enums.coach.book.ingestion.StructureTypeEnum;
import com.sure.question.mapper.CoachBookIngestionStructureMapper;
import com.sure.question.mapper.CoachBookIngestionTocLlmMapper;
import com.sure.question.mapper.IngestionPaperMapper;
import com.sure.question.service.CoachBookIngestionService;
import com.sure.question.service.CoachBookIngestionStructureService;
import com.sure.question.service.LlmService;
import com.sure.question.service.OssManager;
import com.sure.question.util.IdUtil;
import com.sure.question.util.TreeUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.StructureVo;
import com.sure.question.vo.ingestion.TocRecognizeResultVo;
import com.sure.question.vo.ingestion.TocRecognizeVo;
import com.volcengine.ark.runtime.model.completion.chat.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class CoachBookIngestionStructureServiceImpl extends ServiceImpl<CoachBookIngestionStructureMapper, CoachBookIngestionStructure> implements CoachBookIngestionStructureService {
    private final CoachBookIngestionService coachBookIngestionService;
    private final IngestionPaperMapper ingestionPaperMapper;
    private final CoachBookIngestionTocLlmMapper coachBookIngestionTocLlmMapper;
    private final OssManager ossManager;
    private final TransactionTemplate transactionTemplate;
    private final LlmService llmService;

    @Resource
    @Qualifier("tocRecognizeExecutor")
    private ThreadPoolTaskExecutor tocRecognizeExecutor;

    @Override
    public List<StructureVo> getStructures(int ingestionId, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckPermission(ingestionId, userVo);

        List<CoachBookIngestionStructure> coachBookIngestionStructures = this.lambdaQuery()
                .eq(CoachBookIngestionStructure::getIngestionId, ingestionId)
                .list();
        if (coachBookIngestionStructures.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> pageImageUrls = coachBookIngestionService.parsePageImages(ingestion.getPageImages(), false)
                .stream().map(PageImage::getUrl).collect(Collectors.toList());

        List<StructureVo> flattenList = coachBookIngestionStructures.stream().map(structure -> {
            StructureVo vo = new StructureVo();
            vo.setId(structure.getId());
            vo.setStructureType(structure.getStructureType());
            vo.setStructureName(structure.getStructureName());
            vo.setStructureParentId(structure.getStructureParentId());

            List<String> pageImageUrlList = structure.getPageImageUrlList();
            vo.setPageImageUrls(pageImageUrlList.stream().map(ossManager::getPublicPathTK).collect(Collectors.toList()));

            // 生成页码
            List<Integer> pageNumbers = pageImageUrlList.stream()
                    .map(pageImageUrls::indexOf).map(x -> x + 1).sorted()
                    .collect(Collectors.toList());
            vo.setPageNumbers(pageNumbers);
            return vo;
        }).collect(Collectors.toList());

        List<StructureVo> result = TreeUtil.buildTree(flattenList, StructureVo::getId, StructureVo::getStructureParentId, StructureVo::getChildren);

        // 按页码排序
        Comparator<StructureVo> pageNumberComparator = Comparator.<StructureVo>comparingInt(vo -> {
            List<Integer> pageNumbers = vo.getPageNumbers();
            if (pageNumbers == null || pageNumbers.isEmpty()) {
                return -1;
            }
            return pageNumbers.get(0);
        }).thenComparing(StructureVo::getId);
        result.sort(pageNumberComparator);

        TreeUtil.TraverseOptions<StructureVo> traverseOptions = TreeUtil.TraverseOptions.<StructureVo>builder()
                .childrenGetter(vo -> vo.getChildren() == null ? Collections.emptyList() : vo.getChildren())
                .postOrder(node -> {
                    if (node.getChildren() != null) {
                        node.getChildren().sort(pageNumberComparator);
                    }
                    return true;
                })
                .build();
        TreeUtil.traverse(result, traverseOptions);

        return result;
    }

    @Override
    public void saveStructures(int ingestionId, List<StructureVo> structureTree, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);

        // 提取结构
        List<CoachBookIngestionStructure> structures = extractStructures(ingestionId, structureTree);

        // 检查图片
        List<PageImage> pageImages = coachBookIngestionService.parsePageImages(ingestion.getPageImages(), false);
        checkStructurePageImages(structures, pageImages);

        // 与现有结构比较
        List<CoachBookIngestionStructure> dbStructures = this.lambdaQuery()
                .eq(CoachBookIngestionStructure::getIngestionId, ingestionId)
                .list();
        List<CoachBookIngestionStructure> addList = new ArrayList<>();
        List<CoachBookIngestionStructure> removeList = new ArrayList<>();
        List<CoachBookIngestionStructure> updateList = new ArrayList<>();
        List<CoachBookIngestionStructure> updatePageList = new ArrayList<>();
        compareStructure(structures, dbStructures, addList, removeList, updateList, updatePageList);

        // 检查能否删除和修改页面
        Set<Long> removeAndUpdatePageIds = Stream.of(removeList, updatePageList)
                .flatMap(Collection::stream)
                .map(CoachBookIngestionStructure::getId)
                .collect(Collectors.toSet());
        if (!removeAndUpdatePageIds.isEmpty()) {
            List<Long> existsPaperStructureIds = ingestionPaperMapper.selectList(new LambdaQueryWrapper<IngestionPaper>()
                            .eq(IngestionPaper::getIngestionId, ingestionId)
                            .and(wq -> wq.in(IngestionPaper::getMainBodyStructureId, removeAndUpdatePageIds)
                                    .or().in(IngestionPaper::getAnswerStructureId, removeAndUpdatePageIds))
                            .select(IngestionPaper::getMainBodyStructureId))
                    .stream().map(IngestionPaper::getMainBodyStructureId).collect(Collectors.toList());
            if (!existsPaperStructureIds.isEmpty()) {
                Map<Long, String> structureIdNameMap = TreeUtil.getFlattenNodes(structureTree, StructureVo::getChildren).stream()
                        .collect(Collectors.toMap(StructureVo::getId, StructureVo::getStructureName));
                String structureNames = existsPaperStructureIds.stream().map(structureIdNameMap::get).filter(Objects::nonNull).collect(Collectors.joining("、"));
                throw new ApiException("以下章节已划分试卷，不允许删除或修改页面：\n" + structureNames);
            }
        }

        // 保存
        List<Long> deleteIds = new ArrayList<>();
        removeList.forEach(x -> deleteIds.add(x.getId()));
        updateList.forEach(x -> deleteIds.add(x.getId()));

        List<CoachBookIngestionStructure> insertList = new ArrayList<>();
        insertList.addAll(addList);
        insertList.addAll(updateList);

        transactionTemplate.execute(status -> {
            if (!deleteIds.isEmpty()) {
                this.lambdaUpdate()
                        .eq(CoachBookIngestionStructure::getIngestionId, ingestionId)
                        .in(CoachBookIngestionStructure::getId, deleteIds)
                        .remove();
            }
            if (!insertList.isEmpty()) {
                this.saveBatch(insertList);
            }
            return null;
        });
    }

    private List<CoachBookIngestionStructure> extractStructures(int ingestionId, List<StructureVo> structureVoTree) {
        // 遍历
        final boolean[] exitsToc = {false};
        TreeUtil.TraverseOptions<StructureVo> traverseOptions = TreeUtil.TraverseOptions.<StructureVo>builder()
                .childrenGetter(vo -> vo.getChildren() == null ? Collections.emptyList() : vo.getChildren())
                .preOrderWithContext((node, context) -> {
                    if (BaseEnum.getById(StructureTypeEnum.class, node.getStructureType()) == null) {
                        throw new ApiException("结构类型错误");
                    }
                    if (context.parent != null && !Objects.equals(context.parent.getStructureType(), node.getStructureType())) {
                        throw new ApiException("子结构类型须与父级相同");
                    }
                    if (StringUtils.isBlank(node.getStructureName())) {
                        throw new ApiException("结构名称不能为空");
                    }

                    // 目录不允许有父级和子级
                    if (StructureTypeEnum.TOC.getId().equals(node.getStructureType())) {
                        if (exitsToc[0]) {
                            throw new ApiException("目录只能有一个");
                        }
                        exitsToc[0] = true;
                        if (context.parent != null) {
                            throw new ApiException("目录不能有父级");
                        }
                        if (!node.getChildren().isEmpty()) {
                            throw new ApiException("目录不能有子级");
                        }
                    }

                    if (node.getPageImageUrls() == null || node.getPageImageUrls().isEmpty()) {
                        // 叶节点页面不能为空
                        if (context.children.isEmpty()) {
                            throw new ApiException("页面不能为空");
                        }
                    } else {
                        // 替换为OSS对象名
                        node.setPageImageUrls(node.getPageImageUrls().stream().map(ossManager::getObjectNameTK).collect(Collectors.toList()));
                    }


                    // 设置Id和ParentId
                    if (node.getId() == null) {
                        node.setId(IdUtil.longId());
                    }
                    node.setStructureParentId(context.parent == null ? null : context.parent.getId());
                    return true;
                })
                .build();
        TreeUtil.traverse(structureVoTree, traverseOptions);

        List<StructureVo> structureVoList = TreeUtil.getFlattenNodes(structureVoTree, StructureVo::getChildren);
        return structureVoList.stream().map(vo -> {
            CoachBookIngestionStructure structure = new CoachBookIngestionStructure();
            structure.setId(vo.getId());
            structure.setIngestionId(ingestionId);
            structure.setStructureType(vo.getStructureType());
            structure.setStructureName(vo.getStructureName());
            structure.setStructureParentId(vo.getStructureParentId());
            if (vo.getPageImageUrls() != null && !vo.getPageImageUrls().isEmpty()) {
                structure.setPageImageUrlList(vo.getPageImageUrls());
            }
            return structure;
        }).collect(Collectors.toList());
    }

    private void checkStructurePageImages(List<CoachBookIngestionStructure> structures, List<PageImage> pageImages) {
        Set<String> pageImageUrlSet = pageImages.stream().map(PageImage::getUrl).collect(Collectors.toSet());
        structures.forEach(structure -> {
            List<String> imageUrlList = structure.getPageImageUrlList();
            if (imageUrlList == null || imageUrlList.isEmpty()) {
                return;
            }
            if (!pageImageUrlSet.containsAll(imageUrlList)) {
                throw new ApiException("页面图片不存在");
            }
        });
    }

    private void compareStructure(List<CoachBookIngestionStructure> structures, List<CoachBookIngestionStructure> dbStructures,
                                  List<CoachBookIngestionStructure> addList, List<CoachBookIngestionStructure> removeList,
                                  List<CoachBookIngestionStructure> updateList, List<CoachBookIngestionStructure> updatePageList) {
        Map<Long, CoachBookIngestionStructure> dbStructureMap = dbStructures.stream().collect(Collectors.toMap(CoachBookIngestionStructure::getId, Function.identity()));
        structures.forEach(structure -> {
            CoachBookIngestionStructure dbStructure = dbStructureMap.remove(structure.getId());
            if (dbStructure == null) {
                structure.setId(IdUtil.longId());
                addList.add(structure);
            } else {
                if (!dbStructure.equals(structure)) {
                    updateList.add(structure);
                }
                if (!Objects.equals(dbStructure.getPageImageUrlList(), structure.getPageImageUrlList())) {
                    updatePageList.add(structure);
                }
            }
        });
        removeList.addAll(dbStructureMap.values());
    }

    @Override
    public void recognizeTOCStructure(int ingestionId, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);
        CoachBookIngestionStructure toc = this.lambdaQuery()
                .eq(CoachBookIngestionStructure::getIngestionId, ingestionId)
                .eq(CoachBookIngestionStructure::getStructureType, StructureTypeEnum.TOC.getId())
                .one();
        if (toc == null) {
            throw new ApiException("目录不存在");
        }

        // 已经识别完成或正在识别中，则不再发起识别请求
        CoachBookIngestionTocLlm tocLlm = coachBookIngestionTocLlmMapper.selectOne(new LambdaQueryWrapper<CoachBookIngestionTocLlm>()
                .eq(CoachBookIngestionTocLlm::getIngestionId, ingestionId)
                .eq(CoachBookIngestionTocLlm::getStructureId, toc.getId()));
        if (tocLlm != null && (CommonProgressStatusEnum.COMPLETED.getId().equals(tocLlm.getLlmStatus()) || CommonProgressStatusEnum.RUNNING.getId().equals(tocLlm.getLlmStatus()))) {
            return;
        }

        // 准备目录图片
        List<String> urls = toc.getPageImageUrlList().stream().map(ossManager::getPublicPathTK).collect(Collectors.toList());
        if (urls.isEmpty()) {
            throw new ApiException("目录图片不存在");
        }

        // 构建请求
        ChatCompletionRequest request = new ChatCompletionRequest();
        addLLMRequestMessages(request, urls);
        request.setThinking(new ChatCompletionRequest.ChatCompletionRequestThinking("enabled"));
        RequestParam param = RequestParam.builder()
                .request(request)
                .modelName("doubao-1-5-thinking-vision-pro-250428")
                .userId(userVo.getUserId())
                .schoolId(userVo.getSchoolId())
                .businessType("ingestion-toc")
                .businessId(String.valueOf(toc.getId()))
                .build();

        // 更新识别状态
        if (tocLlm == null) {
            tocLlm = CoachBookIngestionTocLlm.builder()
                    .ingestionId(ingestionId)
                    .structureId(toc.getId())
                    .llmStatus(CommonProgressStatusEnum.RUNNING.getId()).build();
            coachBookIngestionTocLlmMapper.insert(tocLlm);
        } else {
            tocLlm.setLlmStatus(CommonProgressStatusEnum.RUNNING.getId());
            coachBookIngestionTocLlmMapper.updateById(tocLlm);
        }

        // 发起识别请求
        CoachBookIngestionTocLlm finalTocLlm = tocLlm;
        tocRecognizeExecutor.submit(() -> {
            try {
                List<TocRecognizeResultVo> result = llmService.chatForList(param, TocRecognizeResultVo.class);
                finalTocLlm.setLlmResult(JSON.toJSONString(result));
                finalTocLlm.setLlmStatus(CommonProgressStatusEnum.COMPLETED.getId());
                coachBookIngestionTocLlmMapper.updateById(finalTocLlm);
            } catch (Exception ex) {
                finalTocLlm.setErrorMessage(ex.getMessage());
                finalTocLlm.setLlmStatus(CommonProgressStatusEnum.FAILED.getId());
                coachBookIngestionTocLlmMapper.updateById(finalTocLlm);
                log.error("【大模型识别目录异常】, ingestionId = {}", ingestionId, ex);
                throw new ApiException("识别目录失败");
            }
        });
    }

    private void addLLMRequestMessages(ChatCompletionRequest request, List<String> imageUrls) {
        List<ChatMessage> chatMessages = new ArrayList<>();
        request.setMessages(chatMessages);

        // 系统角色
        chatMessages.add(ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是一个精通OCR和数据结构化的AI助手。").build());

        // 用户消息
        // 多种类消息列表
        List<ChatCompletionContentPart> multiParts = new ArrayList<>();
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiParts).build();
        chatMessages.add(userMessage);

        // 任务目标
        multiParts.add(ChatCompletionContentPart.builder().type("text").text("# 任务：\n你的任务是精确地识别并解析图片中的教辅书籍目录，包括名称、页码、层级等信息。").build());

        // 目录图片
        multiParts.add(ChatCompletionContentPart.builder().type("text").text("# 目录图片：\n").build());
        imageUrls.forEach(url -> multiParts.add(ChatCompletionContentPart.builder()
                .type("image_url")
                .imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(url))
                .build()));

        ResponseFormatJSONSchemaJSONSchemaParam param = new ResponseFormatJSONSchemaJSONSchemaParam("目录识别结果schema");
        param.setStrict(true);
        String schema = "{\"description\":\"一个书籍目录的结构化表示，组织成一个条目数组\",\"type\":\"array\",\"items\":{\"type\":\"object\",\"description\":\"目录中的单个条目\",\"required\":[\"type\",\"name\",\"page\"],\"properties\":{\"type\":{\"type\":\"string\",\"description\":\"条目类型，必须是 '正文' 或 '答案解析' 之一\",\"enum\":[\"正文\",\"答案解析\"]},\"name\":{\"type\":\"string\",\"description\":\"条目名称\"},\"page\":{\"type\":\"integer\",\"description\":\"页码，没有则为 null\"},\"children\":{\"type\":\"array\",\"description\":\"嵌套的下一级目录条目数组\"}}}}";
        Object jsonSchema = JSON.parseObject(schema);
        param.setSchema(new ObjectMapper().valueToTree(jsonSchema));
        ChatCompletionRequest.ChatCompletionRequestResponseFormat format = new ChatCompletionRequest.ChatCompletionRequestResponseFormat("json_schema", param);
        request.setResponseFormat(format);
    }

    @Override
    public TocRecognizeVo getRecognizeTOCResult(int ingestionId, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckPermission(ingestionId, userVo);
        CoachBookIngestionStructure toc = this.lambdaQuery()
                .eq(CoachBookIngestionStructure::getIngestionId, ingestionId)
                .eq(CoachBookIngestionStructure::getStructureType, StructureTypeEnum.TOC.getId())
                .one();
        if (toc == null) {
            return null;
        }
        CoachBookIngestionTocLlm tocLlm = coachBookIngestionTocLlmMapper.selectOne(new LambdaQueryWrapper<CoachBookIngestionTocLlm>()
                .eq(CoachBookIngestionTocLlm::getIngestionId, ingestionId)
                .eq(CoachBookIngestionTocLlm::getStructureId, toc.getId()));
        if (tocLlm == null) {
            return null;
        }
        return new TocRecognizeVo(tocLlm);
    }
}




