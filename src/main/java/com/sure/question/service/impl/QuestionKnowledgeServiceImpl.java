package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.sure.common.exception.ApiException;
import com.sure.question.document.FullSmallQuestionDoc;
import com.sure.question.dto.llm.RequestParam;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.CommonProgressStatusEnum;
import com.sure.question.enums.GradeLevelEnum;
import com.sure.question.service.KnowledgeMainService;
import com.sure.question.service.LlmService;
import com.sure.question.service.QuestionKnowledgeService;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.HtmlUtil;
import com.sure.question.util.IdUtil;
import com.sure.question.util.TreeUtil;
import com.sure.question.vo.KnowledgeMainVo;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.question.*;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.NodeVisitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class QuestionKnowledgeServiceImpl implements QuestionKnowledgeService {
    private final RestHighLevelClient client;
    private final KnowledgeMainService knowledgeMainService;
    private final RedisTemplate<String, String> redisTemplate;
    private final LlmService llmService;

    @Value("${cacheKey.recommendQuestionKnowledgePre}")
    private String recommendQuestionKnowledgePre;

    @Autowired
    @Qualifier("questionRecommendKnowledgeExecutor")
    private ThreadPoolTaskExecutor questionRecommendKnowledgeExecutor;

    // 连续空白符
    private static final Pattern MULTI_WS = Pattern.compile("\\s+");
    // 噪声词
    private static final Pattern NOISE = Pattern.compile("(以下|下列|正确|不正确|不能|应当|请选择|判断|是|否|哪项|项?是)");

    @Override
    public FindKnowledgeResultVo findQuestionKnowledge(QuestionVo questionVo, int topK) {
        // 准备知识点树
        List<KnowledgeMainVo> knowledgeTree = knowledgeMainService.getGradeLevelSubjectKnowledgeTreeWithCache(questionVo.getStage(), questionVo.getSubjectId());
        List<KnowledgeMainVo> knowledgeList = TreeUtil.getFlattenNodes(knowledgeTree, KnowledgeMainVo::getKnowledgeVoList);
        // 所有知识点名称，按长度倒序排
        List<String> knowledgeNames = knowledgeList.stream().map(KnowledgeMainVo::getKnowledgeName).distinct()
                .sorted(Comparator.comparingInt(String::length).reversed()).collect(Collectors.toList());
        Map<Integer, String> knowledgeIdNameMap = knowledgeList.stream().collect(Collectors.toMap(KnowledgeMainVo::getId, KnowledgeMainVo::getKnowledgeName));

        // 清理材料
        String trunk = cleanSearchContent(questionVo.getTrunk());

        // 遍历小题
        List<FindKnowledgeResultBranchVo> branches = new ArrayList<>();
        for (SmallQuestionVo branch : questionVo.getBranches()) {
            List<FindKnowledgeCandidateVo> candidates = findBranchKnowledge(questionVo, branch, knowledgeNames, knowledgeIdNameMap, trunk, topK);
            branches.add(new FindKnowledgeResultBranchVo(branch.getId(), candidates));
        }
        return new FindKnowledgeResultVo(questionVo.getId(), CommonProgressStatusEnum.COMPLETED.getId(), branches);
    }

    /**
     * 查小题知识点
     */
    private List<FindKnowledgeCandidateVo> findBranchKnowledge(QuestionVo questionVo, SmallQuestionVo smallQuestionVo, List<String> knowledgeNames, Map<Integer, String> knowledgeIdNameMap, String trunk, int topK) {
        String stem = cleanSearchContent(smallQuestionVo.getStem());
        String options = "";
        if (smallQuestionVo.getOptions() != null && !smallQuestionVo.getOptions().isEmpty()) {
            options = smallQuestionVo.getOptions().stream().map(SmallQuestionOptionVo::getContent).map(this::cleanSearchContent).collect(Collectors.joining("\n"));
        }

        // 1. 按知识点名称查找
        List<FindKnowledgeCandidateVo> nameCandidates = findByKnowledgeName(questionVo, knowledgeNames, knowledgeIdNameMap, trunk, stem, options);
        // 2. 找相似提的知识点
        List<FindKnowledgeCandidateVo> questionCandidates = findBySimilarQuestion(questionVo, smallQuestionVo, knowledgeIdNameMap, trunk, stem, options);
        // 3. 合并
        normalizeCandidatesScore(nameCandidates);
        normalizeCandidatesScore(questionCandidates);
        List<FindKnowledgeCandidateVo> candidates = mergeCandidates(nameCandidates, questionCandidates, 0.3, 0.7);
        return candidates.stream().limit(topK).collect(Collectors.toList());
    }

    /**
     * 匹配知识点名称关键字
     */
    @SneakyThrows
    private List<FindKnowledgeCandidateVo> findByKnowledgeName(QuestionVo questionVo, List<String> knowledgeNames, Map<Integer, String> knowledgeIdNameMap, String trunk, String stem, String options) {
        // 1. 从题干/选项/材料中匹配完整知识点名称
        List<String> phrasesFromStem = extractPhrases(knowledgeNames, stem, 8);
        List<String> phrasesFromOptions = extractPhrases(knowledgeNames, options, 6);
        List<String> phrasesFromTrunk = extractPhrases(knowledgeNames, trunk, 6);

        // 2. 组装查询：match_phrase 优先，multi_match 兜底
        BoolQueryBuilder qb = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("grade_level", questionVo.getStage()))
                .filter(QueryBuilders.termQuery("subject_id", questionVo.getSubjectId()))
                .filter(QueryBuilders.termQuery("is_leaf", true));

        // 题干命中术语
        for (String p : phrasesFromStem) {
            qb.should(QueryBuilders.matchPhraseQuery("name", p).slop(0).boost(4.0f));
            qb.should(QueryBuilders.matchPhraseQuery("path", p).slop(0).boost(2.0f));
        }
        // 选项命中术语
        for (String p : phrasesFromOptions) {
            qb.should(QueryBuilders.matchPhraseQuery("name", p).slop(0).boost(2.0f));
            qb.should(QueryBuilders.matchPhraseQuery("path", p).slop(0).boost(1.0f));
        }
        // 材料命中术语
        for (String p : phrasesFromTrunk) {
            qb.should(QueryBuilders.matchPhraseQuery("name", p).slop(0).boost(2.0f));
            qb.should(QueryBuilders.matchPhraseQuery("path", p).slop(0).boost(1.0f));
        }

        // 检索题干
        if (!stem.isEmpty()) {
            // 题干词必须出现
            qb.should(QueryBuilders.multiMatchQuery(stem)
                    .field("name", 1.5f)
                    .field("path", 0.8f)
                    .type(MultiMatchQueryBuilder.Type.BEST_FIELDS)
                    .operator(Operator.AND));
        }
        // 检索选项
        if (!options.isEmpty()) {
            qb.should(QueryBuilders.multiMatchQuery(options)
                    .field("name", 1.5f)
                    .field("path", 0.8f)
                    .type(MultiMatchQueryBuilder.Type.BEST_FIELDS));
        }

        qb.minimumShouldMatch("1");

        SearchSourceBuilder ssb = new SearchSourceBuilder()
                .size(10)
                .trackTotalHits(false)
                .fetchSource(new String[]{"id", "name", "path", "parent_id"}, new String[]{})
                .query(qb);
        SearchRequest req = new SearchRequest("knowledge").source(ssb);
        SearchResponse resp = client.search(req, RequestOptions.DEFAULT);

        List<FindKnowledgeCandidateVo> result = new ArrayList<>();
        for (SearchHit hit : resp.getHits().getHits()) {
            Map<String, Object> s = hit.getSourceAsMap();
            if (s == null) continue;
            int id = asInteger(s.get("id"));
            result.add(new FindKnowledgeCandidateVo(id, knowledgeIdNameMap.get(id), (double) hit.getScore(), 1));
        }
        return result;
    }

    /**
     * 清理搜索内容
     */
    private String cleanSearchContent(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        String s = stripHtml(content);
        // nbsp
        s = s.replace('\u00A0', ' ');
        // 连续空白符替换为空格
        s = MULTI_WS.matcher(s).replaceAll(" ").trim();
        // 去噪声
        s = NOISE.matcher(s).replaceAll(" ");
        return s;
    }

    /**
     * 去除html标签
     */
    private String stripHtml(String content) {
        Document doc = Jsoup.parse(content);

        // 删除所有 <math> 元素
        doc.select("math").remove();

        // 遍历所有文本节点，删除其中以 $ 开头和结尾的 LaTeX 公式
        doc.traverse(new NodeVisitor() {
            @Override
            public void head(Node node, int depth) {
                if (node instanceof TextNode) {
                    TextNode textNode = (TextNode) node;
                    // 使用正则表达式删除 $...$ 之间的内容
                    String newText = textNode.getWholeText().replaceAll("\\$[^$]+\\$", "");
                    textNode.text(newText);
                }
            }

            @Override
            public void tail(Node node, int depth) {
                // 不需要在尾部处理
            }
        });

        return doc.text();
    }

    private List<String> extractPhrases(Collection<String> phrases, String text, int topK) {
        return phrases.stream().filter(text::contains).limit(topK).collect(Collectors.toList());
    }

    /**
     * 匹配相似题目知识点
     */
    @SneakyThrows
    private List<FindKnowledgeCandidateVo> findBySimilarQuestion(QuestionVo questionVo, SmallQuestionVo smallQuestionVo, Map<Integer, String> knowledgeIdNameMap, String trunk, String stem, String options) {
        BoolQueryBuilder qb = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("grade_level", questionVo.getStage()))
                .filter(QueryBuilders.termQuery("subject_id", questionVo.getSubjectId()))
                .filter(QueryBuilders.existsQuery("knowledge_ids"));
        // 题型
//        if (questionVo.getType() != null) {
//            qb.filter(QueryBuilders.termQuery("question_type_id", questionVo.getType()));
//        }
        // 小题题型
        if (smallQuestionVo.getType() != null) {
            qb.filter(QueryBuilders.termQuery("small_question_type_id", smallQuestionVo.getType()));
        }

        // 主召回：multi_match(S + O)
        if (StringUtils.isNotBlank(stem) || StringUtils.isNotBlank(options)) {
            String qCombined = (stem + "\n" + options).trim();
            if (!qCombined.isEmpty()) {
                MultiMatchQueryBuilder mm = QueryBuilders.multiMatchQuery(qCombined)
                        .field("stem_html", 3.0F)
                        .field("material_html", 1.5F)
                        .field("options_html", 1.0F)
                        .field("text_no_answer", 0.5F)
                        .type(MultiMatchQueryBuilder.Type.BEST_FIELDS);
                qb.should(mm);
            }
        }
//        // 题干刚性匹配（AND）
//        if (StringUtils.isNotBlank(stem)) {
//            qb.should(QueryBuilders.matchQuery("stem_html", stem)
//                    .operator(Operator.AND)
//                    .boost(1.0f));
//            qb.should(QueryBuilders.matchPhraseQuery("stem_html", stem)
//                    .slop(2)
//                    .boost(0.6f));
//        }
        // 材料，太长时忽略
        if (StringUtils.isNotBlank(trunk) && trunk.length() < 300) {
            qb.should(QueryBuilders.matchQuery("material_html", trunk).boost(1.5F));
        }
        qb.minimumShouldMatch(1);

        SearchSourceBuilder ssb = new SearchSourceBuilder()
                .query(qb)
                .size(100)
                .trackTotalHits(false)
                .fetchSource(new String[]{
                        "small_question_id","question_id","knowledge_ids","use_count","year",
                        "question_type_id","small_question_type_id"
                }, new String[]{});

        // 按question_id去重
        ssb.collapse(new CollapseBuilder("question_id"));

        SearchRequest req = new SearchRequest("small_question").source(ssb);
        SearchResponse resp = client.search(req, RequestOptions.DEFAULT);

        // 4) 收集相似小题，用于权重归一化
        List<FullSmallQuestionDoc> similarSmallQuestions = new ArrayList<>();
        Map<Long, Float> smallQuestionIdScoreMap = new HashMap<>();
        for (SearchHit hit : resp.getHits().getHits()) {
            Map<String, Object> src = hit.getSourceAsMap();
            if (src == null) continue;

            List<Long> knowledgeIds = asLongList(src.get("knowledge_ids"));
            if (knowledgeIds.isEmpty()) continue;

            FullSmallQuestionDoc doc = new FullSmallQuestionDoc();
            doc.setSmall_question_id(asLong(src.get("small_question_id")));
            doc.setKnowledge_ids(knowledgeIds);
            doc.setYear(asShort(src.get("year")));
            doc.setUse_count(asInteger(src.get("use_count")));
            doc.setQuestion_type_id(asInteger(src.get("question_type_id")));
            doc.setSmall_question_type_id(asShort(src.get("small_question_type_id")));
            similarSmallQuestions.add(doc);
            smallQuestionIdScoreMap.put(doc.getSmall_question_id(), hit.getScore());
        }
        if (similarSmallQuestions.isEmpty()) {
            return Collections.emptyList();
        }

        // 5) 计算投票
        Short minYear = similarSmallQuestions.stream().map(FullSmallQuestionDoc::getYear).filter(Objects::nonNull).min(Short::compareTo).orElse(null);
        Short maxYear = similarSmallQuestions.stream().map(FullSmallQuestionDoc::getYear).filter(Objects::nonNull).max(Short::compareTo).orElse(null);
        Map<Long, FindKnowledgeCandidateVo> candidateMap = new HashMap<>();
        for (FullSmallQuestionDoc doc : similarSmallQuestions) {
            // es打分权重
            double wEs = smallQuestionIdScoreMap.get(doc.getSmall_question_id());
            // 使用次数权重
            double wUse = 1.0 + 0.02 * Math.log1p(doc.getUse_count() == null ? 0 : doc.getUse_count());
            // 年份权重
            double wYear = 1.0;
            if (doc.getYear() != null && minYear != null && maxYear != null && !minYear.equals(maxYear)) {
                wYear = 1.0 + 0.05 * ((doc.getYear() - minYear) / (double) (maxYear - minYear));
            }
            double wType = 1;
            if (questionVo.getType() != null && questionVo.getType().equals(doc.getQuestion_type_id())) {
                Integer docSmallQuestionTypeId = doc.getSmall_question_type_id() == null ? null : doc.getSmall_question_type_id().intValue();
                if (smallQuestionVo.getType() != null && smallQuestionVo.getType().equals(docSmallQuestionTypeId)) {
                    wType = 1.1;
                }
            }

            double w = wEs * wUse * wYear * wType;
            // 均分，避免多知识点的小题过度占优
            w = w / doc.getKnowledge_ids().size();
            for (Long knowledgeId : doc.getKnowledge_ids()) {
                FindKnowledgeCandidateVo candidate = candidateMap.computeIfAbsent(knowledgeId,  k -> new FindKnowledgeCandidateVo(k.intValue(), knowledgeIdNameMap.get(k.intValue()), 0.0, 0));
                candidate.setScore(candidate.getScore() + w);
                candidate.setSupport(candidate.getSupport() + 1);
            }
        }

        return candidateMap.values().stream().sorted(FindKnowledgeCandidateVo::compareTo).collect(Collectors.toList());
    }

    private void normalizeCandidatesScore(List<FindKnowledgeCandidateVo> candidates) {
        if (candidates.isEmpty()) {
            return;
        }
        double maxScore = candidates.stream().mapToDouble(FindKnowledgeCandidateVo::getScore).max().orElse(1);
        double minScore = candidates.stream().mapToDouble(FindKnowledgeCandidateVo::getScore).min().orElse(0);
        candidates.forEach(c -> c.setScore((c.getScore() - minScore) / (maxScore - minScore)));
    }

    private List<FindKnowledgeCandidateVo> mergeCandidates(List<FindKnowledgeCandidateVo> candidates1, List<FindKnowledgeCandidateVo> candidates2, double weight1, double weight2) {
        Map<Integer, FindKnowledgeCandidateVo> candidateMap = new HashMap<>();
        candidates1.forEach(c -> {
            FindKnowledgeCandidateVo candidate = candidateMap.computeIfAbsent(c.getKnowledgeId(), k -> new FindKnowledgeCandidateVo(k, c.getKnowledgeName(), 0.0, 0));
            candidate.setScore(candidate.getScore() + c.getScore() * weight1);
            candidate.setSupport(candidate.getSupport() + c.getSupport());
        });
        candidates2.forEach(c -> {
            FindKnowledgeCandidateVo candidate = candidateMap.computeIfAbsent(c.getKnowledgeId(), k -> new FindKnowledgeCandidateVo(k, c.getKnowledgeName(), 0.0, 0));
            candidate.setScore(candidate.getScore() + c.getScore() * weight2);
            candidate.setSupport(candidate.getSupport() + c.getSupport());
        });
        List<FindKnowledgeCandidateVo> result = candidateMap.values().stream()
                .sorted(FindKnowledgeCandidateVo::compareTo)
                .collect(Collectors.toList());
        normalizeCandidatesScore(result);
        return result;
    }

    static List<Long> asLongList(Object o) {
        if (o == null) return Collections.emptyList();
        if (o instanceof List) {
            List<?> l = (List<?>) o;
            List<Long> out = new ArrayList<>(l.size());
            for (Object e : l) {
                if (e instanceof Number) out.add(((Number) e).longValue());
                else try { out.add(Long.parseLong(String.valueOf(e))); } catch (Exception ignored) {}
            }
            return out;
        } else if (o instanceof Number) {
            return Collections.singletonList(((Number) o).longValue());
        } else {
            try { return Collections.singletonList(Long.parseLong(String.valueOf(o))); }
            catch (Exception e) { return Collections.emptyList(); }
        }
    }
    static Long asLong(Object o) {
        if (o == null) return null;
        if (o instanceof Number) return ((Number) o).longValue();
        try { return Long.parseLong(String.valueOf(o)); } catch (Exception e) { return null; }
    }
    static Integer asInteger(Object o) {
        if (o == null) return null;
        if (o instanceof Number) return ((Number) o).intValue();
        try { return Integer.parseInt(String.valueOf(o)); } catch (Exception e) { return null; }
    }
    static Short asShort(Object o) {
        if (o == null) return null;
        if (o instanceof Number) return ((Number) o).shortValue();
        try { return Short.parseShort(String.valueOf(o)); } catch (Exception e) { return null; }
    }

    @Override
    public String recommendQuestionKnowledgeToken(QuestionVo questionVo, TokenUserVo userVo) {
        if (questionVo.getBranches() == null || questionVo.getBranches().isEmpty()) {
            throw new ApiException("无小题");
        }
        if (questionVo.getStage() == null || questionVo.getSubjectId() == null) {
            throw new ApiException("学段学科不能为空");
        }

        // 构建执行上下文
        RecommendQuestionKnowledgeContext context = buildRecommendKnowledgeContext(questionVo, userVo);

        // token缓存10分钟
        FindKnowledgeResultVo result = FindKnowledgeResultVo.builder().questionId(questionVo.getId()).status(CommonProgressStatusEnum.RUNNING.getId()).build();
        redisTemplate.opsForValue().set(context.redisKey, JSON.toJSONString(result), 10, TimeUnit.MINUTES);

        // 每小题单独线程执行
        List<CompletableFuture<List<FindKnowledgeCandidateVo>>> futures = new ArrayList<>();
        for (SmallQuestionVo smallQuestionVo : questionVo.getBranches()) {
            CompletableFuture<List<FindKnowledgeCandidateVo>> future = CompletableFuture
                    .supplyAsync(() -> recommendBranchKnowledgeUseLLM(context, smallQuestionVo), questionRecommendKnowledgeExecutor)
                    .exceptionally(ex -> {
                        log.error("推荐知识点失败, gradeLevel = {}, subjectId = {}, questionId = {}, branchId = {}",
                                context.questionVo.getStage(), context.questionVo.getSubjectId(), context.questionVo.getId(), smallQuestionVo.getId(), ex);
                        return null;
                    });
            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allFutures.thenRunAsync(() -> {
                    List<List<FindKnowledgeCandidateVo>> candidatesList = futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
                    finalizeRecommendQuestionKnowledge(context, candidatesList);
                }, questionRecommendKnowledgeExecutor)
                .exceptionally(ex -> {
                    log.error("推荐知识点失败, gradeLevel = {}, subjectId = {}, questionId = {}",
                            context.questionVo.getStage(), context.questionVo.getSubjectId(), context.questionVo.getId(), ex);
                    return null;
                });

        return context.token;
    }

    @Override
    public FindKnowledgeResultVo recommendQuestionKnowledgeResult(String token) {
        String json = redisTemplate.opsForValue().get(getRecommendQuestionRedisKey(token));
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JSON.parseObject(json, FindKnowledgeResultVo.class);
    }

    private RecommendQuestionKnowledgeContext buildRecommendKnowledgeContext(QuestionVo questionVo, TokenUserVo userVo) {
        RecommendQuestionKnowledgeContext context = new RecommendQuestionKnowledgeContext();
        context.userVo = userVo;
        context.questionVo = questionVo;

        // 准备知识点树
        List<KnowledgeMainVo> knowledgeTree = knowledgeMainService.getGradeLevelSubjectKnowledgeTreeWithCache(questionVo.getStage(), questionVo.getSubjectId());
        List<KnowledgeMainVo> knowledgeList = TreeUtil.getFlattenNodes(knowledgeTree, KnowledgeMainVo::getKnowledgeVoList);
        // 所有知识点名称，按长度倒序排
        List<String> knowledgeNames = knowledgeList.stream().map(KnowledgeMainVo::getKnowledgeName).distinct()
                .sorted(Comparator.comparingInt(String::length).reversed()).collect(Collectors.toList());
        Map<Integer, String> knowledgeIdNameMap = knowledgeList.stream().collect(Collectors.toMap(KnowledgeMainVo::getId, KnowledgeMainVo::getKnowledgeName));
        context.knowledgeNames = knowledgeNames;
        context.knowledgeIdNameMap = knowledgeIdNameMap;

        // 清理材料
        context.trunkForEs = cleanSearchContent(questionVo.getTrunk());
        context.trunkForLLM = cleanLLMPromptContent(questionVo.getTrunk());

        context.token = IdUtil.StringId();
        context.redisKey = getRecommendQuestionRedisKey(context.token);
        return context;
    }

    private static class RecommendQuestionKnowledgeContext {
        public TokenUserVo userVo;
        public QuestionVo questionVo;
        public List<String> knowledgeNames;
        public Map<Integer, String> knowledgeIdNameMap;
        public String trunkForEs;
        public String trunkForLLM;
        public String token;
        public String redisKey;
    }

    private String getRecommendQuestionRedisKey(String token) {
        return recommendQuestionKnowledgePre + token;
    }

    private List<FindKnowledgeCandidateVo> recommendBranchKnowledgeUseLLM(RecommendQuestionKnowledgeContext context, SmallQuestionVo smallQuestionVo) {
        // 取20个候选
        long t = System.currentTimeMillis();
        List<FindKnowledgeCandidateVo> candidates = findBranchKnowledge(context.questionVo, smallQuestionVo, context.knowledgeNames, context.knowledgeIdNameMap, context.trunkForEs, 20);
        log.info("推荐知识点，从es查找候选知识点耗时：{}ms", System.currentTimeMillis() - t);
        if (candidates.isEmpty()) {
            log.error("推荐知识点异常, 无候选知识点, gradeLevel = {}, subjectId = {}, questionId = {}, branchId = {}",
                    context.questionVo.getStage(), context.questionVo.getSubjectId(), context.questionVo.getId(), smallQuestionVo.getId());
            return Collections.emptyList();
        }

        RequestParam requestParam = new RequestParam();
        requestParam.setRequest(buildRecommendKnowledgeLLMRequest(context, smallQuestionVo, candidates));
        requestParam.setModelName("doubao-seed-1-6-250615");
        requestParam.setUserId(context.userVo.getUserId());
        requestParam.setSchoolId(context.userVo.getSchoolId());
        requestParam.setBusinessType("recommend-question-knowledge");
        requestParam.setBusinessId(context.questionVo.getId() + "-" + smallQuestionVo.getId());

        t = System.currentTimeMillis();
        List<Integer> orders = llmService.chatForList(requestParam, Integer.class);
        log.info("推荐知识点，调用大模型耗时：{}ms", System.currentTimeMillis() - t);
        if (orders.isEmpty()) {
            log.error("推荐知识点异常, 大模型返回结果为空, gradeLevel = {}, subjectId = {}, questionId = {}, branchId = {}",
                    context.questionVo.getStage(), context.questionVo.getSubjectId(), context.questionVo.getId(), smallQuestionVo.getId());
            return Collections.emptyList();
        }

        List<FindKnowledgeCandidateVo> result = orders.stream().filter(order -> order > 0 && order <= candidates.size())
                .distinct()
                .map(order -> candidates.get(order - 1)).collect(Collectors.toList());
        if (result.size() != orders.size()) {
            log.error("推荐知识点异常, 大模型返回结果不在候选知识点中, gradeLevel = {}, subjectId = {}, questionId = {}, branchId = {}, candidates = {}, orders = {}",
                    context.questionVo.getStage(), context.questionVo.getSubjectId(), context.questionVo.getId(), smallQuestionVo.getId(), JSON.toJSONString(candidates), JSON.toJSONString(orders));
        }
        return result;
    }

    private ChatCompletionRequest buildRecommendKnowledgeLLMRequest(RecommendQuestionKnowledgeContext context, SmallQuestionVo smallQuestionVo, List<FindKnowledgeCandidateVo> candidates) {
        ChatCompletionRequest request = new ChatCompletionRequest();
        List<ChatMessage> chatMessages = new ArrayList<>();
        request.setMessages(chatMessages);

        // 系统角色
        String gradeLevelName = BaseEnum.getNameById(GradeLevelEnum.class, context.questionVo.getStage());
        String subjectName = CacheUtil.getSubjectName(context.questionVo.getSubjectId());
        String systemRole = "你是一位资深的" + gradeLevelName + subjectName + "教研专家。你的任务是为题目精准地标注知识点。\n";
        chatMessages.add(ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(systemRole).build());

        // 题目内容
        List<ChatCompletionContentPart> multiParts = new ArrayList<>();
        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiParts).build();
        chatMessages.add(userMessage);

        multiParts.add(ChatCompletionContentPart.builder().type("text").text("# 题目内容：\n").build());
        if (StringUtils.isNotBlank(context.trunkForLLM)) {
            multiParts.add(ChatCompletionContentPart.builder().type("text").text(context.trunkForLLM + "\n\n").build());
        }
        StringBuilder stemAndOptionsBuilder = new StringBuilder(cleanLLMPromptContent(smallQuestionVo.getStem()));
        if (smallQuestionVo.getOptions() != null && !smallQuestionVo.getOptions().isEmpty()) {
            for (SmallQuestionOptionVo option : smallQuestionVo.getOptions()) {
                stemAndOptionsBuilder.append("\n").append(option.getLabel()).append(". ").append(cleanLLMPromptContent(option.getContent()));
            }
        }
        if (stemAndOptionsBuilder.length() > 0) {
            String stemAndOptions = stemAndOptionsBuilder.toString();
            if (context.questionVo.getBranches().size() > 1) {
                int code = context.questionVo.getBranches().indexOf(smallQuestionVo) + 1;
                stemAndOptions = "【小题" + code + "】" + stemAndOptions;
            }
            multiParts.add(ChatCompletionContentPart.builder().type("text").text(stemAndOptions + "\n\n").build());
        }

        // 候选知识点
        multiParts.add(ChatCompletionContentPart.builder().type("text").text("# 候选知识点列表：\n").build());
        StringBuilder candidatesBuilder = new StringBuilder();
        for (int i = 0; i < candidates.size(); i++) {
            candidatesBuilder.append(i + 1).append(". ").append(candidates.get(i).getKnowledgeName()).append("\n");
        }
        multiParts.add(ChatCompletionContentPart.builder().type("text").text(candidatesBuilder.toString()).build());

        // 任务说明
        multiParts.add(ChatCompletionContentPart.builder().type("text").text("# 任务说明：\n").build());
        String tips = "";
        if (context.questionVo.getBranches().size() > 1) {
            tips += "1. 仔细阅读题目内容，理解当前小题的核心考点；\n";
        } else {
            tips += "1. 仔细阅读题目内容，理解其核心考点；\n";
        }
        tips += "2. 从候选知识点列表中选择最合适的知识点；\n";
        tips += "3. 返回候选知识点名称前面的序号组成的数组。\n";
        tips += "4. **不要**选择任何不在候选列表中的知识点。\n";
        tips += "5. 如果候选列表中没有一个合适的知识点，请返回一个空数组 `[]`。\n";
        multiParts.add(ChatCompletionContentPart.builder().type("text").text(tips).build());

        // 示例
        multiParts.add(ChatCompletionContentPart.builder().type("text").text("# 示例：\n").build());
        String example = "题目内容：在直角三角形中，两直角边长分别为3和4，求斜边长。\n候选知识点列表：\n1. 三角函数\n2. 勾股定理\n3. 平行线性质\n你的输出应为：\n[2]\n";
        multiParts.add(ChatCompletionContentPart.builder().type("text").text(example).build());

        // 响应格式
        String schema = "{\"type\":\"array\",\"items\":{\"type\":\"integer\",\"minimum\":1,\"maximum\":" + candidates.size() + ",\"uniqueItems\":true}}";
        request.setResponseFormat(llmService.buildResponseFormat("知识点结果schema", schema));

        // 温度
        request.setTemperature(0.0);

        // 输出上限
        request.setMaxTokens(128);

        // 禁用推理
        request.setThinking(new ChatCompletionRequest.ChatCompletionRequestThinking("disabled"));

        return request;
    }

    private String cleanLLMPromptContent(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        return HtmlUtil.cleanToMarkdown(content, new HtmlUtil.CleanToMarkdownOptions());
    }

    private void finalizeRecommendQuestionKnowledge(RecommendQuestionKnowledgeContext context, List<List<FindKnowledgeCandidateVo>> candidatesList) {
        FindKnowledgeResultVo result = new FindKnowledgeResultVo();
        result.setQuestionId(context.questionVo.getId());
        result.setStatus(CommonProgressStatusEnum.COMPLETED.getId());

        List<FindKnowledgeResultBranchVo> branches = new ArrayList<>();
        for (int i = 0; i < context.questionVo.getBranches().size(); i++) {
            String branchId = context.questionVo.getBranches().get(i).getId();
            List<FindKnowledgeCandidateVo> candidates = candidatesList.get(i);
            // 只要有一个小题推荐知识点失败，则整个题目失败
            if (candidates == null || candidates.isEmpty()) {
                result.setStatus(CommonProgressStatusEnum.FAILED.getId());
                candidates = Collections.emptyList();
            }
            branches.add(new FindKnowledgeResultBranchVo(branchId, candidates));
        }
        result.setBranches(branches);

        redisTemplate.opsForValue().set(context.redisKey, JSON.toJSONString(result));
    }
}
