package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperOcrRequest;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperOcrResponse;
import com.aliyun.teaopenapi.models.Config;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.ocr.aliyun.Figure;
import com.sure.question.dto.ocr.aliyun.Point;
import com.sure.question.dto.ocr.aliyun.paper.PaperBody;
import com.sure.question.dto.ocr.aliyun.paper.WordInfo;
import com.sure.question.dto.ocr.common.Element;
import com.sure.question.dto.ocr.common.Question;
import com.sure.question.entity.CoachBookIngestion;
import com.sure.question.entity.IngestionPageOcr;
import com.sure.question.enums.CommonProgressStatusEnum;
import com.sure.question.mapper.IngestionPageOcrMapper;
import com.sure.question.service.CoachBookIngestionService;
import com.sure.question.service.IngestionPageOcrService;
import com.sure.question.service.OssManager;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.IngestionPageOcrVo;
import com.sure.question.vo.ingestion.IngestionQuestionOcrVo;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.QuestionInfo;
import com.tencentcloudapi.ocr.v20181119.models.QuestionSplitOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.QuestionSplitOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.ResultList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class IngestionPageOcrServiceImpl extends ServiceImpl<IngestionPageOcrMapper, IngestionPageOcr> implements IngestionPageOcrService{
    private final CoachBookIngestionService coachBookIngestionService;
    private final OssManager ossManager;

    @Resource
    @Qualifier("ingestionPageOcrExecutor")
    private ThreadPoolTaskExecutor ingestionPageOcrExecutor;
    @Resource
    @Qualifier("ingestionQuestionOcrExecutor")
    private ThreadPoolTaskExecutor ingestionQuestionOcrExecutor;

    @Value("${aliyun.ocr.accessKeyId}")
    private String aliyunAccessKeyId;
    @Value("${aliyun.ocr.accessKeySecret}")
    private String aliyunAccessKeySecret;
    @Value("${aliyun.ocr.endpoint}")
    private String aliyunEndpoint;
    private Client aliyunOcrClient;

    @Value("${tencentCloud.ocr.secretId}")
    private String tencentCloudSecretId;
    @Value("${tencentCloud.ocr.secretKey}")
    private String tencentCloudSecretKey;
    private OcrClient tencentCloudOcrClient;

    private static final Map<Integer, String> OCRParamSubjectMap = new HashMap<Integer, String>() {{
        put(1, "Chinese");
        put(2, "Math");
        put(3, "English");
        put(4, "Politics");
        put(15, "Politics");
        put(5, "History");
        put(6, "Geography");
        put(7, "Physics");
        put(8, "Chemistry");
        put(9, "Biology");
    }};
    private static final Map<Integer, String> OCRParamGradeLevelMap = new HashMap<Integer, String>() {{
        put(1, "PrimarySchool_");
        put(2, "JHighSchool_");
        put(4, "JHighSchool_");
    }};

    @PostConstruct
    private void init() throws Exception {
        createALiYunOcrClient();
        createTencentCloudOcrClient();
    }

    private void createALiYunOcrClient() throws Exception {
        Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(aliyunAccessKeyId)
                .setAccessKeySecret(aliyunAccessKeySecret)
                .setEndpoint(aliyunEndpoint);
        this.aliyunOcrClient = new Client(config);
    }

    private void createTencentCloudOcrClient() {
        // 实例化一个认证对象
        Credential cred = new Credential(tencentCloudSecretId, tencentCloudSecretKey);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("ocr.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        this.tencentCloudOcrClient = new OcrClient(cred, "", clientProfile);
    }

    @Override
    public List<IngestionPageOcrVo> getIngestionPageOcrResultList(int ingestionId, Collection<String> pageImageUrls, TokenUserVo userVo) {
        return getIngestionPageOcrResultListCommon(ingestionId, pageImageUrls, userVo, IngestionPageOcr::getPageOcrStatus, IngestionPageOcr::getPageOcrResult)
                .stream().map(IngestionPageOcrVo::new).collect(Collectors.toList());
    }

    @Override
    public List<IngestionQuestionOcrVo> getIngestionQuestionOcrResultList(int ingestionId, Collection<String> pageImageUrls, TokenUserVo userVo) {
        return getIngestionPageOcrResultListCommon(ingestionId, pageImageUrls, userVo, IngestionPageOcr::getQuestionOcrStatus, IngestionPageOcr::getQuestionOcrResult)
                .stream().map(IngestionQuestionOcrVo::new).collect(Collectors.toList());
    }

    @SafeVarargs
    private final List<IngestionPageOcr> getIngestionPageOcrResultListCommon(int ingestionId, Collection<String> pageImageUrls, TokenUserVo userVo, SFunction<IngestionPageOcr, ?>... cols) {
        coachBookIngestionService.getIngestionCheckPermission(ingestionId, userVo);
        List<String> pageImageUrlList = pageImageUrls == null ? null : pageImageUrls.stream().map(ossManager::getObjectNameTK).collect(Collectors.toList());

        List<SFunction<IngestionPageOcr, ?>> selectList = new ArrayList<>();
        selectList.add(IngestionPageOcr::getIngestionId);
        selectList.add(IngestionPageOcr::getPageImageUrl);
        selectList.addAll(Arrays.asList(cols));
        @SuppressWarnings("unchecked")
        SFunction<IngestionPageOcr, ?>[] selectArray = selectList.toArray(new SFunction[0]);
        List<IngestionPageOcr> ocrList = this.lambdaQuery()
                .eq(IngestionPageOcr::getIngestionId, ingestionId)
                .in(pageImageUrlList != null && !pageImageUrlList.isEmpty(), IngestionPageOcr::getPageImageUrl, pageImageUrlList)
                .select(selectArray)
                .list();
        ocrList.forEach(item -> item.setPageImageUrl(ossManager.getPublicPathTK(item.getPageImageUrl())));
        return ocrList;
    }

    @Override
    public void startPageOcr(int ingestionId, List<String> pageImageUrls, TokenUserVo userVo) {
        pageImageUrls = pageImageUrls.stream().map(ossManager::getObjectNameTK).collect(Collectors.toList());
        // 新增
        CoachBookIngestion ingestion = startOcrCommon(ingestionId, pageImageUrls, userVo);
        // 更新状态
        this.baseMapper.updatePageOcrStatusToWaiting(ingestionId, pageImageUrls);
        // 提交任务
        pageImageUrls.forEach(pageImageUrl -> ingestionPageOcrExecutor.submit(() ->
                doPageOcr(ingestionId, pageImageUrl, ingestion.getGradeLevel(), ingestion.getSubjectId())));
    }

    @Override
    public void startQuestionOcr(int ingestionId, List<String> pageImageUrls, TokenUserVo userVo) {
        pageImageUrls = pageImageUrls.stream().map(ossManager::getObjectNameTK).collect(Collectors.toList());
        // 新增
        startOcrCommon(ingestionId, pageImageUrls, userVo);
        // 更新状态
        this.baseMapper.updateQuestionOcrStatusToWaiting(ingestionId, pageImageUrls);
        // 提交任务
        pageImageUrls.forEach(pageImageUrl -> ingestionQuestionOcrExecutor.submit(() ->
                doQuestionOcr(ingestionId, pageImageUrl)));
    }

    private CoachBookIngestion startOcrCommon(int ingestionId, List<String> pageImageUrls, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);

        // 新增
        Set<String> dbPageImageUrls = this.lambdaQuery()
                .eq(IngestionPageOcr::getIngestionId, ingestionId)
                .in(IngestionPageOcr::getPageImageUrl, pageImageUrls)
                .select(IngestionPageOcr::getPageImageUrl)
                .list().stream().map(IngestionPageOcr::getPageImageUrl).collect(Collectors.toSet());
        List<IngestionPageOcr> insertList = pageImageUrls.stream()
                .filter(x -> !dbPageImageUrls.contains(x))
                .map(pageImageUrl -> {
                    IngestionPageOcr ingestionPageOcr = new IngestionPageOcr();
                    ingestionPageOcr.setIngestionId(ingestionId);
                    ingestionPageOcr.setPageImageUrl(pageImageUrl);
                    return ingestionPageOcr;
                }).collect(Collectors.toList());
        if (!insertList.isEmpty()) {
            this.saveBatch(insertList);
        }

        return ingestion;
    }

    private void doPageOcr(int ingestionId, String pageImageUrl, int gradeLevel, int subjectId) {
        int updated = this.baseMapper.updatePageOcrStatusToRunning(ingestionId, pageImageUrl);
        if (updated == 0) {
            return;
        }
        try {
            String rawResult = recognizeEduPaperOCR(ossManager.getPublicPathTK(pageImageUrl), gradeLevel, subjectId);
            Date endTime = new Date();
            String result = buildALiYunEduPageOcrResult(rawResult);
            this.lambdaUpdate().eq(IngestionPageOcr::getIngestionId, ingestionId)
                    .eq(IngestionPageOcr::getPageImageUrl, pageImageUrl)
                    .set(IngestionPageOcr::getPageOcrStatus, CommonProgressStatusEnum.COMPLETED.getId())
                    .set(IngestionPageOcr::getPageOcrMethod, "aliyunEduPaperOcr")
                    .set(IngestionPageOcr::getPageOcrEndTime, endTime)
                    .set(IngestionPageOcr::getPageOcrResult, result)
                    .set(IngestionPageOcr::getPageOcrRawResult, rawResult)
                    .update();
        } catch (Exception e) {
            log.error("整页识别失败, ingestionId = {}, pageImageUrl = {}", ingestionId, pageImageUrl, e);
            this.lambdaUpdate().eq(IngestionPageOcr::getIngestionId, ingestionId)
                    .eq(IngestionPageOcr::getPageImageUrl, pageImageUrl)
                    .set(IngestionPageOcr::getPageOcrStatus, CommonProgressStatusEnum.FAILED.getId())
                    .update();
        }
    }

    private String recognizeEduPaperOCR(String imageUrl, int gradeLevelId, int subjectId) throws Exception {
        RecognizeEduPaperOcrRequest request = new RecognizeEduPaperOcrRequest();
        request.setUrl(imageUrl);
        request.setImageType("scan");
        request.setSubject(getOCRSubjectParam(gradeLevelId, subjectId));
        request.setOutputOricoord(true);
        RecognizeEduPaperOcrResponse response = aliyunOcrClient.recognizeEduPaperOcr(request);
        if (response.getStatusCode() != 200) {
            throw new ApiException("ocr接口返回失败, 错误码: " + response.getStatusCode());
        }
        return response.getBody().getData();
    }

    private String getOCRSubjectParam(int gradeLevelId, int subjectId) {
        String subjectName = OCRParamSubjectMap.get(subjectId);
        if (StringUtils.isEmpty(subjectName)) {
            return "default";
        }
        String gradeLevelName = OCRParamGradeLevelMap.get(gradeLevelId);
        if (StringUtils.isEmpty(gradeLevelName)) {
            return subjectName;
        }
        return gradeLevelName + subjectName;
    }

    private String buildALiYunEduPageOcrResult(String rawResult) {
        PaperBody body = JSON.parseObject(rawResult, PaperBody.class);
        List<WordInfo> wordInfoList = body.getPrism_wordsInfo();
        List<Figure> figures = body.getFigure();
        List<Element> elements = new ArrayList<>(wordInfoList.size() + figures.size());
        for (WordInfo wordInfo : wordInfoList) {
            Element element = new Element();
            element.type = "text";
            element.content = wordInfo.getWord();
            if (wordInfo.isRecClassifyFormula() && StringUtils.isNotEmpty(element.content)) {
                if (!element.content.startsWith("$")) {
                    element.content = "$" + element.content;
                }
                if (!element.content.endsWith("$")) {
                    element.content = element.content + "$";
                }
            }
            setElementPosition(wordInfo.getPos(), element);
            elements.add(element);
        }
        for (Figure figure : figures) {
            // 图片
            if (figure.isImage()) {
                Element element = new Element();
                element.type = "image";
                setElementPosition(figure.getPoints(), element);
                elements.add(element);
            }
            // TODO 解析表格
            else if (figure.isTable()) {
                Element element = new Element();
                element.type = "table";
                setElementPosition(figure.getPoints(), element);
                elements.add(element);
            }
        }
        return JSON.toJSONString(elements);
    }

    private void setElementPosition(List<Point> points, Element element) {
        int left = Integer.MAX_VALUE;
        int right = 0;
        int top = Integer.MAX_VALUE;
        int bottom = 0;
        for (Point p : points) {
            if (p.getX() < left) {
                left = p.getX();
            }
            if (p.getX() > right) {
                right = p.getX();
            }
            if (p.getY() < top) {
                top = p.getY();
            }
            if (p.getY() > bottom) {
                bottom = p.getY();
            }
        }
        element.x = left;
        element.y = top;
        element.width = right - left;
        element.height = bottom - top;
    }

    private void doQuestionOcr(int ingestionId, String pageImageUrl) {
        int updated = this.baseMapper.updateQuestionOcrStatusToRunning(ingestionId, pageImageUrl);
        if (updated == 0) {
            return;
        }
        try {
            QuestionSplitOCRRequest req = new QuestionSplitOCRRequest();
            req.setImageUrl(ossManager.getPublicPathTK(pageImageUrl));
            req.setEnableImageCrop(false);
            req.setEnableOnlyDetectBorder(false);

            QuestionSplitOCRResponse res = tencentCloudOcrClient.QuestionSplitOCR(req);
            Date endTime = new Date();

            String rawResult = JSON.toJSONString(res);
            String result = buildTencentCloudQuestionOcrResult(res.getQuestionInfo());
            this.lambdaUpdate().eq(IngestionPageOcr::getIngestionId, ingestionId)
                    .eq(IngestionPageOcr::getPageImageUrl, pageImageUrl)
                    .set(IngestionPageOcr::getQuestionOcrStatus, CommonProgressStatusEnum.COMPLETED.getId())
                    .set(IngestionPageOcr::getQuestionOcrMethod, "tencentCloudQuestionSplitOcr")
                    .set(IngestionPageOcr::getQuestionOcrEndTime, endTime)
                    .set(IngestionPageOcr::getQuestionOcrResult, result)
                    .set(IngestionPageOcr::getQuestionOcrRawResult, rawResult)
                    .update();
        } catch (Exception e) {
            log.error("划题识别失败, ingestionId = {}, pageImageUrl = {}", ingestionId, pageImageUrl, e);
            this.lambdaUpdate().eq(IngestionPageOcr::getIngestionId, ingestionId)
                    .eq(IngestionPageOcr::getPageImageUrl, pageImageUrl)
                    .set(IngestionPageOcr::getQuestionOcrStatus, CommonProgressStatusEnum.FAILED.getId())
                    .update();
        }
    }

    private String buildTencentCloudQuestionOcrResult(QuestionInfo[] questionInfoArray) {
        List<Question> questionList = new ArrayList<>();
        for (QuestionInfo questionInfo : questionInfoArray) {
            for (ResultList resultList : questionInfo.getResultList()) {
                questionList.add(buildOneQuestion(resultList));
            }
        }
        return JSON.toJSONString(questionList);
    }

    private Question buildOneQuestion(ResultList resultList) {
        Question question = new Question();

        // 题干
        com.tencentcloudapi.ocr.v20181119.models.Element[] questionElements = resultList.getQuestion();
        if (questionElements != null && questionElements.length > 0) {
            question.stem = new ArrayList<>();
            for (com.tencentcloudapi.ocr.v20181119.models.Element questionElement : questionElements) {
                question.stem.addAll(buildElement(questionElement, "text", false));
                if ("multiple-choice".equals(questionElement.getGroupType())) {
                    question.type = "choice";
                } else if ("fill-in-the-blank".equals(questionElement.getGroupType())) {
                    question.type = "fillBlank";
                } else {
                    question.type = "essay";
                }
                if (questionElement.getResultList() != null && questionElement.getResultList().length > 0) {
                    if (question.children == null) {
                        question.children = new ArrayList<>();
                    }
                    for (ResultList childResultList : questionElement.getResultList()) {
                        question.children.add(buildOneQuestion(childResultList));
                    }
                }
            }
        }
        // 题干图片
        if (resultList.getFigure() != null && resultList.getFigure().length > 0) {
            if (question.stem == null) {
                question.stem = new ArrayList<>();
            }
            for (com.tencentcloudapi.ocr.v20181119.models.Element figureElement : resultList.getFigure()) {
                question.stem.addAll(buildElement(figureElement, "image", true));
            }
        }
        // 题干/材料表格
        if (resultList.getTable() != null && resultList.getTable().length > 0) {
            if (question.stem == null) {
                question.stem = new ArrayList<>();
            }
            for (com.tencentcloudapi.ocr.v20181119.models.Element tableElement : resultList.getTable()) {
                question.stem.addAll(buildElement(tableElement, "table", true));
            }
        }

        // 选项
        if (resultList.getOption() != null && resultList.getOption().length > 0) {
            question.options = Arrays.stream(resultList.getOption()).map(element -> buildElement(element, "text", false))
                    .flatMap(Collection::stream).collect(Collectors.toList());
        }
        // 答案
        if (resultList.getAnswer() != null && resultList.getAnswer().length > 0) {
            question.answer = Arrays.stream(resultList.getAnswer()).map(element -> buildElement(element, "text", false))
                    .flatMap(Collection::stream).collect(Collectors.toList());
        }
        // 解析
        if (resultList.getParse() != null && resultList.getParse().length > 0) {
            question.explanation = Arrays.stream(resultList.getParse()).map(element -> buildElement(element, "text", false))
                    .flatMap(Collection::stream).collect(Collectors.toList());
        }
        return question;
    }

    private List<Element> buildElement(com.tencentcloudapi.ocr.v20181119.models.Element element, String type, boolean recursive) {
        List<Element> result = new ArrayList<>();
        Element newElement = new Element();
        newElement.type = type;
        if ("text".equals(type)) {
            newElement.content = element.getText();
        }
        setElementCoordinate(element.getCoord(), newElement);
        result.add(newElement);
        if (recursive && element.getResultList() != null && element.getResultList().length > 0) {
            result.addAll(extractResultListArrayAllElements(element.getResultList()));
        }
        return result;
    }

    private void setElementCoordinate(com.tencentcloudapi.ocr.v20181119.models.Polygon polygon, Element result) {
        long minX = Integer.MAX_VALUE;
        long maxX = 0;
        long minY = Integer.MAX_VALUE;
        long maxY = 0;
        List<com.tencentcloudapi.ocr.v20181119.models.Coord> coordList = Stream.of(polygon.getLeftTop(), polygon.getLeftBottom(), polygon.getRightTop(), polygon.getRightBottom())
                .filter(coord -> coord != null && coord.getX() != null && coord.getY() != null).collect(Collectors.toList());
        if (coordList.isEmpty()) {
            return;
        }
        for (com.tencentcloudapi.ocr.v20181119.models.Coord coord : coordList) {
            if (coord.getX() < minX) {
                minX = coord.getX();
            }
            if (coord.getX() > maxX) {
                maxX = coord.getX();
            }
            if (coord.getY() < minY) {
                minY = coord.getY();
            }
            if (coord.getY() > maxY) {
                maxY = coord.getY();
            }
        }
        long width = maxX - minX;
        long height = maxY - minY;
        if (width <= 0 || height <= 0) {
            return;
        }
        result.x = (int) minX;
        result.y = (int) minY;
        result.width = (int) width;
        result.height = (int) height;
    }

    private List<Element> extractResultListArrayAllElements(ResultList[] resultListArray) {
        if (resultListArray == null || resultListArray.length == 0) {
            return Collections.emptyList();
        }
        List<Element> result = new ArrayList<>();
        for (ResultList resultList : resultListArray) {
            Stream.of(resultList.getQuestion(), resultList.getOption(), resultList.getAnswer(), resultList.getParse(), resultList.getFigure(), resultList.getTable())
                    .filter(elements -> elements != null && elements.length > 0)
                    .forEach(elements -> {
                        String type = "text";
                        if (elements == resultList.getFigure()) {
                            type = "image";
                        } else if (elements == resultList.getTable()) {
                            type = "table";
                        }
                        for (com.tencentcloudapi.ocr.v20181119.models.Element element : elements) {
                            result.addAll(buildElement(element, type, true));
                        }
                    });
        }
        return result;
    }
}




