package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.coachBook.ingestion.PageArea;
import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.entity.CoachBookIngestion;
import com.sure.question.entity.CoachBookIngestionStructure;
import com.sure.question.entity.IngestionPaper;
import com.sure.question.entity.PaperQuestion;
import com.sure.question.enums.coach.book.ingestion.StructureTypeEnum;
import com.sure.question.mapper.CoachBookIngestionStructureMapper;
import com.sure.question.mapper.IngestionPaperMapper;
import com.sure.question.mapper.PaperQuestionMapper;
import com.sure.question.service.CoachBookIngestionService;
import com.sure.question.service.IngestionPaperService;
import com.sure.question.service.OssManager;
import com.sure.question.util.IdUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.IngestionPaperVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class IngestionPaperServiceImpl extends ServiceImpl<IngestionPaperMapper, IngestionPaper> implements IngestionPaperService {
    private final CoachBookIngestionService coachBookIngestionService;
    private final CoachBookIngestionStructureMapper coachBookIngestionStructureMapper;
    private final PaperQuestionMapper paperQuestionMapper;
    private final OssManager ossManager;

    @Override
    public List<IngestionPaperVo> getPapers(int ingestionId, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckPermission(ingestionId, userVo);

        // 查试卷
        List<IngestionPaper> papers = this.lambdaQuery()
                .eq(IngestionPaper::getIngestionId, ingestionId)
                .list();
        if (papers.isEmpty()) {
            return Collections.emptyList();
        }

        // 构建结果
        List<IngestionPaperVo> result = new ArrayList<>();
        for (IngestionPaper paper : papers) {
            IngestionPaperVo vo = new IngestionPaperVo(paper);
            // 设置页码
            vo.getMainBodyAreas().forEach(p -> p.setUrl(ossManager.getPublicPathTK(p.getUrl())));
            vo.getAnswerAreas().forEach(p -> p.setUrl(ossManager.getPublicPathTK(p.getUrl())));
            result.add(vo);
        }
        return result;
    }

    @Override
    public void addPapers(int ingestionId, List<IngestionPaperVo> papers, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);
        papers.forEach(item -> item.setPaperId(null));
        checkPapers(ingestion, papers);
        List<IngestionPaper> newPapers = buildPapers(ingestion, papers);
        this.saveBatch(newPapers);
    }

    private void checkPapers(CoachBookIngestion ingestion, List<IngestionPaperVo> papers) {
        List<CoachBookIngestionStructure> structures = coachBookIngestionStructureMapper.selectList(new LambdaQueryWrapper<CoachBookIngestionStructure>()
                .eq(CoachBookIngestionStructure::getIngestionId, ingestion.getId()));
        if (structures.isEmpty()) {
            throw new ApiException("请先划分章节结构");
        }

        Map<String, PageImage> pageImageMap = coachBookIngestionService.parsePageImages(ingestion.getPageImages(), false)
                .stream().collect(Collectors.toMap(PageImage::getUrl, Function.identity()));
        List<IngestionPaper> dbPapers = this.lambdaQuery().eq(IngestionPaper::getIngestionId, ingestion.getId()).list();
        Map<Long, CoachBookIngestionStructure> structureMap = structures.stream()
                .collect(Collectors.toMap(CoachBookIngestionStructure::getId, Function.identity()));
        Map<Long, IngestionPaper> structureIdPaperMap = dbPapers.stream()
                .collect(Collectors.toMap(IngestionPaper::getMainBodyStructureId, Function.identity()));
        Set<Long> mainBodyStructureIdSet = new HashSet<>();
        papers.forEach(item -> {
            if (item.getMainBodyStructureId() == null) {
                throw new ApiException("正文对应章节不能为空");
            }
            if (mainBodyStructureIdSet.contains(item.getMainBodyStructureId())) {
                throw new ApiException("一个章节不允许创建多份试卷");
            } else {
                mainBodyStructureIdSet.add(item.getMainBodyStructureId());
            }
            CoachBookIngestionStructure mainBodyStructure = structureMap.get(item.getMainBodyStructureId());
            if (mainBodyStructure == null) {
                throw new ApiException("正文对应章节不存在");
            }
            IngestionPaper dbMainBodyPaper = structureIdPaperMap.get(item.getMainBodyStructureId());
            if (dbMainBodyPaper != null && !dbMainBodyPaper.getPaperId().equals(item.getPaperId())) {
                throw new ApiException("正文对应章节已存在试卷：" + mainBodyStructure.getStructureName());
            }
            if (!StructureTypeEnum.MAIN_BODY.getId().equals(mainBodyStructure.getStructureType())) {
                throw new ApiException("试卷正文对应结构类型应为正文");
            }
            if (item.getMainBodyAreas() == null || item.getMainBodyAreas().isEmpty()) {
                throw new ApiException("正文页面不能为空");
            }
            List<String> mainBodyPageImageUrlList = mainBodyStructure.getPageImageUrlList();
            for (PageArea mainBodyArea : item.getMainBodyAreas()) {
                if (StringUtils.isNotEmpty(mainBodyArea.getUrl())) {
                    mainBodyArea.setUrl(ossManager.getObjectNameTK(mainBodyArea.getUrl()));
                }
                if (!mainBodyPageImageUrlList.contains(mainBodyArea.getUrl())) {
                    throw new ApiException("正文页面不在章节范围内");
                }
                checkPageAreaRectsInBoundary(mainBodyArea, pageImageMap);
            }

            if (item.getAnswerStructureId() == null) {
                return;
            }
            CoachBookIngestionStructure answerStructure = structureMap.get(item.getAnswerStructureId());
            if (answerStructure == null) {
                throw new ApiException("答案对应结构不存在");
            }
            if (!StructureTypeEnum.ANSWER.getId().equals(answerStructure.getStructureType())) {
                throw new ApiException("答案对应结构类型应为答案");
            }
            if (item.getAnswerAreas() == null || item.getAnswerAreas().isEmpty()) {
                return;
            }
            List<String> answerPageImageUrlList = answerStructure.getPageImageUrlList();
            for (PageArea answerArea : item.getAnswerAreas()) {
                if (StringUtils.isNotEmpty(answerArea.getUrl())) {
                    answerArea.setUrl(ossManager.getObjectNameTK(answerArea.getUrl()));
                }
                if (!answerPageImageUrlList.contains(answerArea.getUrl())) {
                    throw new ApiException("答案页面不在对应结构范围内");
                }
                checkPageAreaRectsInBoundary(answerArea, pageImageMap);
            }
        });
    }

    /**
     * 检查页面区域是否在页面范围内
     */
    private void checkPageAreaRectsInBoundary(PageArea area, Map<String, PageImage> pageImageMap) {
        PageImage pageImage = pageImageMap.get(area.getUrl());
        if (pageImage == null) {
            throw new ApiException("页面不存在");
        }
        if (area.getRects() == null || area.getRects().isEmpty()) {
            return;
        }
        area.getRects().forEach(rect -> {
            if (rect.x < 0 || rect.x + rect.width > pageImage.getW()) {
                throw new ApiException("页面区域超出页面范围");
            }
            if (rect.y < 0 || rect.y + rect.height > pageImage.getH()) {
                throw new ApiException("页面区域超出页面范围");
            }
        });
    }

    private List<IngestionPaper> buildPapers(CoachBookIngestion ingestion, List<IngestionPaperVo> papers) {
        return papers.stream().map(item -> {
            IngestionPaper paper = new IngestionPaper();
            paper.setPaperId(item.getPaperId() == null ? IdUtil.longId() : item.getPaperId());
            paper.setIngestionId(ingestion.getId());
            paper.setMainBodyStructureId(item.getMainBodyStructureId());
            paper.setAnswerStructureId(item.getAnswerStructureId());
            paper.setMainBodyAreas(JSON.toJSONString(item.getMainBodyAreas()));
            paper.setAnswerAreas(JSON.toJSONString(item.getAnswerAreas()));
            paper.setConfirmed(false);
            return paper;
        }).collect(Collectors.toList());
    }

    @Override
    public void deletePapers(int ingestionId, List<Long> paperIds, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);

        Set<Long> idSet = new HashSet<>(paperIds);
        List<IngestionPaper> ingestionPapers = this.lambdaQuery()
                .eq(IngestionPaper::getIngestionId, ingestionId)
                .in(IngestionPaper::getPaperId, idSet)
                .list();
        if (ingestionPapers.size() != idSet.size()) {
            throw new ApiException("试卷不存在");
        }

        Map<Long, IngestionPaper> paperMap = ingestionPapers.stream()
                .filter(p -> p.getPaperId() != null)
                .collect(Collectors.toMap(IngestionPaper::getPaperId, Function.identity()));
        if (!paperMap.isEmpty()) {
            List<Long> existsQuestionPaperIds = paperQuestionMapper.selectList(new QueryWrapper<PaperQuestion>()
                            .select("DISTINCT paper_id")
                            .lambda().in(PaperQuestion::getPaperId, paperMap.keySet()))
                    .stream().map(PaperQuestion::getPaperId).map(Long::valueOf).collect(Collectors.toList());
            if (!existsQuestionPaperIds.isEmpty()) {
                List<Long> structureIds = existsQuestionPaperIds.stream().map(paperMap::get).map(IngestionPaper::getMainBodyStructureId).collect(Collectors.toList());
                List<CoachBookIngestionStructure> structures = coachBookIngestionStructureMapper.selectList(new LambdaQueryWrapper<CoachBookIngestionStructure>()
                        .eq(CoachBookIngestionStructure::getIngestionId, ingestionId)
                        .in(CoachBookIngestionStructure::getId, structureIds));
                String structureNames = structures.stream().map(CoachBookIngestionStructure::getStructureName).collect(Collectors.joining("\n"));
                throw new ApiException("以下试卷已有题目，不允许修改：\n" + structureNames);
            }
        }

        this.lambdaUpdate()
                .eq(IngestionPaper::getIngestionId, ingestionId)
                .in(IngestionPaper::getPaperId, paperIds)
                .remove();
    }

    @Override
    public void changePaper(int ingestionId, IngestionPaperVo vo, TokenUserVo userVo) {
        CoachBookIngestion ingestion = coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);

        IngestionPaper ingestionPaper = this.lambdaQuery()
                .eq(IngestionPaper::getIngestionId, ingestionId)
                .eq(IngestionPaper::getPaperId, vo.getPaperId())
                .one();
        if (ingestionPaper == null) {
            throw new ApiException("试卷不存在");
        }

        checkPapers(ingestion, Collections.singletonList(vo));
        IngestionPaper updatePaper = buildPapers(ingestion, Collections.singletonList(vo)).get(0);
        this.updateById(updatePaper);
    }

    @Override
    public void confirmPaperPageAreas(int ingestionId, long paperId, TokenUserVo userVo) {
        coachBookIngestionService.getIngestionCheckNotFinishedNotDeleted(ingestionId, userVo);

        IngestionPaper ingestionPaper = this.lambdaQuery()
                .eq(IngestionPaper::getIngestionId, ingestionId)
                .eq(IngestionPaper::getPaperId, paperId)
                .one();
        if (ingestionPaper == null) {
            throw new ApiException("试卷不存在");
        }

        List<PageArea> mainBodyAreas = null;
        if (StringUtils.isNotEmpty(ingestionPaper.getMainBodyAreas())) {
            mainBodyAreas = JSON.parseArray(ingestionPaper.getMainBodyAreas(), PageArea.class);
        }
        if (mainBodyAreas == null || mainBodyAreas.isEmpty()) {
            throw new ApiException("正文页面不能为空");
        }

        this.lambdaUpdate()
                .eq(IngestionPaper::getIngestionId, ingestionId)
                .eq(IngestionPaper::getPaperId, paperId)
                .set(IngestionPaper::getConfirmed, true)
                .update();
    }
}
