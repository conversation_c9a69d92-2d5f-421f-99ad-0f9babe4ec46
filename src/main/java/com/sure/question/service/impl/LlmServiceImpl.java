package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sure.question.config.LLMConfig;
import com.sure.question.dto.llm.RequestParam;
import com.sure.question.entity.LlmApiLog;
import com.sure.question.enums.CommonProgressStatusEnum;
import com.sure.question.mapper.LlmApiLogMapper;
import com.sure.question.service.LlmService;
import com.volcengine.ark.runtime.exception.ArkHttpException;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ResponseFormatJSONSchemaJSONSchemaParam;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class LlmServiceImpl implements LlmService {
    private final LlmApiLogMapper llmApiLogMapper;
    private final LLMConfig llmConfig;
    private ArkService arkClient;
    private ObjectMapper objectMapper;

    @PostConstruct
    private void init() {
        ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
        Dispatcher dispatcher = new Dispatcher();
        arkClient = ArkService.builder().connectionPool(connectionPool).dispatcher(dispatcher).apiKey(llmConfig.getApiKey()).build();

        objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @SneakyThrows(JsonProcessingException.class)
    @Override
    public ChatCompletionResult chat(RequestParam param) {
        param.check();
        LLMConfig.Model model = llmConfig.getModelByName(param.getModelName());
        if (model == null) {
            throw new IllegalArgumentException("模型不存在");
        }
        ChatCompletionRequest request = param.getRequest();
        request.setModel(model.getName());

        LlmApiLog llmApiLog = new LlmApiLog();
        llmApiLog.setUserId(param.getUserId());
        llmApiLog.setSchoolId(param.getSchoolId());
        llmApiLog.setBusinessType(param.getBusinessType());
        llmApiLog.setBusinessId(param.getBusinessId());
        llmApiLog.setProvider(model.getProvider());
        llmApiLog.setModelName(model.getName());
        llmApiLog.setRequestBody(objectMapper.writeValueAsString(request));
        llmApiLog.setRequestTime(new Date());

        ChatCompletionResult result;
        try {
            result = arkClient.createChatCompletion(request);
        } catch (Exception ex) {
            if (ex instanceof ArkHttpException) {
                llmApiLog.setErrorCode(((ArkHttpException) ex).code);
                llmApiLog.setErrorMessage(ex.getMessage());
            }
            llmApiLog.setStatus(CommonProgressStatusEnum.FAILED.getId());
            llmApiLog.setCreateTime(new Date());
            llmApiLogMapper.insert(llmApiLog);
            throw ex;
        }

        llmApiLog.setStatus(CommonProgressStatusEnum.COMPLETED.getId());
        llmApiLog.setResponseTime(new Date());
        llmApiLog.setResponseBody(objectMapper.writeValueAsString(result));
        String finishReason = null;
        if (result.getChoices() != null && !result.getChoices().isEmpty()) {
            finishReason = result.getChoices().get(0).getFinishReason();
        }
        llmApiLog.setFinishReason(finishReason);

        if (result.getUsage() != null) {
            llmApiLog.setPromptTokens((int) result.getUsage().getPromptTokens());
            llmApiLog.setCompletionTokens((int) result.getUsage().getCompletionTokens());
            llmApiLog.setTotalTokens((int) result.getUsage().getTotalTokens());
            if (result.getUsage().getCompletionTokensDetails() != null) {
                llmApiLog.setReasoningTokens(result.getUsage().getCompletionTokensDetails().getReasoningTokens());
            }
            llmApiLog.setCost(llmConfig.calculateCost(model, llmApiLog.getPromptTokens(), llmApiLog.getCompletionTokens()));
        }

        llmApiLog.setCreateTime(new Date());
        llmApiLogMapper.insert(llmApiLog);

        return result;
    }

    @Override
    public String chatForString(RequestParam param) {
        ChatCompletionResult result = chat(param);
        if (result.getChoices() == null) {
            return null;
        }
        StringBuilder combinedContent = new StringBuilder();
        result.getChoices().forEach(choice -> {
            String content = (String) choice.getMessage().getContent();
            combinedContent.append(content);
        });
        return combinedContent.toString();
    }

    @SneakyThrows
    @Override
    public <T> T chatForObject(RequestParam requestParam, Class<T> clazz) {
        String jsonContent = chatForString(requestParam);
        if (StringUtils.isEmpty(jsonContent)) {
            return null;
        }
        return objectMapper.readValue(jsonContent, clazz);
    }

    @SneakyThrows
    @Override
    public <T> List<T> chatForList(RequestParam requestParam, Class<T> clazz) {
        String jsonContent = chatForString(requestParam);
        if (StringUtils.isEmpty(jsonContent)) {
            return Collections.emptyList();
        }
        JavaType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
        return objectMapper.readValue(jsonContent, listType);
    }

    @Override
    public ChatCompletionRequest.ChatCompletionRequestResponseFormat buildResponseFormat(String schemaName, String jsonSchema) {
        ResponseFormatJSONSchemaJSONSchemaParam param = new ResponseFormatJSONSchemaJSONSchemaParam(schemaName);
        param.setStrict(true);
        Object schemaObject = JSON.parseObject(jsonSchema);
        param.setSchema(new ObjectMapper().valueToTree(schemaObject));
        return new ChatCompletionRequest.ChatCompletionRequestResponseFormat("json_schema", param);
    }
}
