package com.sure.question.service;

import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.question.FindKnowledgeResultVo;
import com.sure.question.vo.question.QuestionVo;

public interface QuestionKnowledgeService {
    /**
     * 找题目知识点
     */
    FindKnowledgeResultVo findQuestionKnowledge(QuestionVo questionVo, int topK);

    /**
     * 推荐题目知识点Token
     */
    String recommendQuestionKnowledgeToken(QuestionVo questionVo, TokenUserVo userVo);

    /**
     * 推荐题目知识点结果
     */
    FindKnowledgeResultVo recommendQuestionKnowledgeResult(String token);
}
