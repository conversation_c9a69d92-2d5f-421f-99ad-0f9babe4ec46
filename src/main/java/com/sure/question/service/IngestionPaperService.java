package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.IngestionPaper;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ingestion.IngestionPaperVo;

import java.util.List;

public interface IngestionPaperService extends IService<IngestionPaper> {
    /**
     * 获取试卷
     */
    List<IngestionPaperVo> getPapers(int ingestionId, TokenUserVo userVo);

    /**
     * 添加试卷
     */
    void addPapers(int ingestionId, List<IngestionPaperVo> papers, TokenUserVo userVo);

    /**
     * 删除试卷
     */
    void deletePapers(int ingestionId, List<Long> paperIds, TokenUserVo userVo);

    /**
     * 修改试卷
     */
    void changePaper(int ingestionId, IngestionPaperVo paper, TokenUserVo userVo);

    /**
     * 确认试卷页面范围
     */
    void confirmPaperPageAreas(int ingestionId, long paperId, TokenUserVo userVo);
}
