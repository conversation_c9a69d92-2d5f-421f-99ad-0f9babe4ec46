package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 教辅录入试卷
 */
@TableName(value ="ingestion_paper")
@Data
public class IngestionPaper {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long paperId;

    /**
     * 录入Id
     */
    private Integer ingestionId;

    /**
     * 正文结构Id
     */
    private Long mainBodyStructureId;

    /**
     * 答案结构Id
     */
    private Long answerStructureId;

    /**
     * 正文页面区域
     */
    private String mainBodyAreas;

    /**
     * 答案页面区域
     */
    private String answerAreas;

    /**
     * 是否已确认
     */
    private Boolean confirmed;
}