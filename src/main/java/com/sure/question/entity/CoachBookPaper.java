package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "coach_book_paper")
public class CoachBookPaper {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 试卷id
     */
    private String paperId;

    /**
     * 教辅id
     */
    private Integer coachBookId;

    /**
     * 排序码
     */
    private Integer sortCode;

    /**
     * 试卷别名
     */
    private String paperAlias;

    /**
     * 是否启用分值统计
     */
    private Boolean scoreAnalysis;

    /**
     * 开放显示时间
     */
    private Date openTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否锁定
     */
    private Boolean locked;

    /**
     * 试卷正文pdf
     */
    private String contentPdfUrl;
    private String contentPdfUploadBy;
    private Date contentPdfUploadTime;
    private String contentPdfSize;
    private Integer contentPdfPages;

    /**
     * 试卷答案pdf
     */
    private String answerPdfUrl;
    private String answerPdfUploadBy;
    private Date answerPdfUploadTime;
    private String answerPdfSize;
    private Integer answerPdfPages;

    /**
     * 听力语音
     */
    private String listeningAudioUrl;
    private String listeningAudioUploadBy;
    private Date listeningAudioUploadTime;

    /**
     * 答题卡pdf
     */
    private String sheetPdfUrl;
    private String sheetPdfUploadBy;
    private Date sheetPdfUploadTime;
    private String sheetPdfSize;
    private Integer sheetPdfPages;
}
