package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 教辅录入
 */
@TableName(value ="coach_book_ingestion")
@Data
@Builder
public class CoachBookIngestion {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 教辅Id
     */
    private Integer coachBookId;

    /**
     * 学段
     */
    private Integer gradeLevel;

    /**
     * 学科
     */
    private Integer subjectId;

    /**
     * 上传文件类型
     */
    private String fileType;

    /**
     * 上传文件OSS路径，多个文件以逗号分割
     */
    private String filePath;

    /**
     * 页面图片Json
     */
    private String pageImages;

    /**
     * pdf页数
     */
    private Integer pdfPageCount;

    /**
     * pdf转图片状态：I-未开始；R-进行中；C-完成；F-失败
     */
    private String pdfToImageStatus;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 上传者
     */
    private String uploadBy;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 是否已完成
     */
    private Boolean finished;

    /**
     * 是否已删除
     */
    private Boolean deleted;
}