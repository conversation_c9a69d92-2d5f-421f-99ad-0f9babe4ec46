package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 大模型调用记录
 */
@TableName(value ="llm_api_log")
@Data
public class LlmApiLog {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID，用于关联多轮对话
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户所在学校ID
     */
    private String schoolId;

    /**
     * 业务类型/场景
     */
    private String businessType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 服务提供商
     */
    private String provider;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 原始请求体
     */
    private String requestBody;

    /**
     * 原始响应体
     */
    private String responseBody;

    /**
     * 调用状态
     */
    private String status;

    /**
     * API返回的错误码
     */
    private String errorCode;

    /**
     * 详细的错误描述
     */
    private String errorMessage;

    /**
     * 模型生成结束的原因
     */
    private String finishReason;

    /**
     * 请求发起时间
     */
    private Date requestTime;

    /**
     * 收到完整响应时间
     */
    private Date responseTime;

    /**
     * 首Token耗时(ms)
     */
    private Integer firstTokenMs;

    /**
     * 总耗时(ms)
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer durationMs;

    /**
     * 输入Token数
     */
    private Integer promptTokens;

    /**
     * 输出Token数
     */
    private Integer completionTokens;

    /**
     * 推理Token数
     */
    private Integer reasoningTokens;

    /**
     * 总Token数
     */
    private Integer totalTokens;

    /**
     * 本次调用的费用(元)
     */
    private BigDecimal cost;

    /**
     * 创建时间
     */
    private Date createTime;
}