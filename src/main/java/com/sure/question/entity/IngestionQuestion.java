package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 录入题目
 */
@TableName(value ="ingestion_question")
@Data
public class IngestionQuestion {
    /**
     * 题目Id
     */
    @TableId
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /**
     * 录入Id
     */
    private Integer ingestionId;

    /**
     * 试卷Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperId;

    /**
     * 正文页面区域
     */
    private String mainBodyAreas;

    /**
     * 正文内容
     */
    private String mainBodyContent;

    /**
     * 答案页面区域
     */
    private String answerAreas;

    /**
     * 答案内容
     */
    private String answerContent;
}