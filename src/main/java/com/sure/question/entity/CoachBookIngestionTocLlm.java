package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 目录识别结果
 */
@TableName(value ="coach_book_ingestion_toc_llm")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CoachBookIngestionTocLlm {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer ingestionId;

    private Long structureId;

    /**
     * 大模型识别状态
     */
    private String llmStatus;

    /**
     * 大模型识别结果
     */
    private String llmResult;

    /**
     * 错误信息
     */
    private String errorMessage;
}