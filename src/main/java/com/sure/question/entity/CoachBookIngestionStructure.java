package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 教辅录入结构
 */
@TableName(value ="coach_book_ingestion_structure")
@Data
public class CoachBookIngestionStructure {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 录入Id
     */
    private Integer ingestionId;

    /**
     * 结构类型
     */
    private String structureType;

    /**
     * 结构名称
     */
    private String structureName;

    /**
     * 父结构Id
     */
    private Long structureParentId;

    /**
     * 页面图片Url，以逗号分割
     */
    private String pageImageUrls;

    public void setPageImageUrlList(List<String> pageImageUrlList) {
        if (pageImageUrlList == null || pageImageUrlList.isEmpty()) {
            this.pageImageUrls = null;
            return;
        }
        this.pageImageUrls = pageImageUrlList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining(","));
    }

    public List<String> getPageImageUrlList() {
        if (StringUtils.isEmpty(pageImageUrls)) {
            return Collections.emptyList();
        }
        return Stream.of(pageImageUrls.split(",")).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    }
}