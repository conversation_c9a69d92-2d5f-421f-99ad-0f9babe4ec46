package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 录入试卷页面识别
 */
@TableName(value ="ingestion_page_ocr")
@Data
public class IngestionPageOcr {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 录入Id
     */
    private Integer ingestionId;

    /**
     * 页面图片url
     */
    private String pageImageUrl;

    /**
     * 整页识别状态
     */
    private String pageOcrStatus;

    /**
     * 整页识别方式
     */
    private String pageOcrMethod;

    /**
     * 整页识别开始时间
     */
    private Date pageOcrStartTime;

    /**
     * 整页识别结束时间
     */
    private Date pageOcrEndTime;

    /**
     * 整页识别结果
     */
    private String pageOcrResult;

    /**
     * 整页识别原始结果
     */
    private String pageOcrRawResult;

    /**
     * 题目划分识别状态
     */
    private String questionOcrStatus;

    /**
     * 题目划分识别方式
     */
    private String questionOcrMethod;

    /**
     * 题目划分识别开始时间
     */
    private Date questionOcrStartTime;

    /**
     * 题目划分识别结束时间
     */
    private Date questionOcrEndTime;

    /**
     * 题目划分识别结果
     */
    private String questionOcrResult;

    /**
     * 题目划分识别原始结果
     */
    private String questionOcrRawResult;
}