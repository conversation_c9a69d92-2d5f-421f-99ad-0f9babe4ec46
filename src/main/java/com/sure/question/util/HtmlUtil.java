package com.sure.question.util;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;

import java.util.Arrays;
import java.util.stream.Collectors;

public class HtmlUtil {

    /**
     * 将 HTML 清洗为适合 LLM 输入的精炼 Markdown 文本
     * 设计目标：
     * 1) 安全清洗（删 script/style/iframe 等），保留必要文本结构。
     * 2) 段落/列表/换行结构化，图片转占位摘要，表格转 Markdown。
     * 3) 数学保真（sup/sub、MathML 占位、公式图片 alt/data-latex）。
     * 4) 控制长度（token 预算），尽量保留关键信息。
     */
    public static String cleanToMarkdown(String html, CleanToMarkdownOptions opts) {
        if (StringUtils.isBlank(html)) {
            return html;
        }
        if (opts == null) {
            opts = new CleanToMarkdownOptions();
        }

        Document doc = Jsoup.parse(html);
        doc.outputSettings(new Document.OutputSettings().prettyPrint(false));

        // 1) 安全清洗：删除明显无关/危险标签
        removeDangerousNodes(doc);

        // 2) 保留必要属性，清理多余属性
        sanitizeAttributes(doc);

        // 3) 数学 & 上下标 -> 文本化
        convertSupSub(doc);
        convertMathMl(doc);

        // 4) 表格 -> Markdown
        convertTablesToMarkdown(doc, opts);

        // 5) 图片 -> 摘要占位
        convertImages(doc, opts);

        // 6) 生成 Markdown 文本：段落/列表/标题/换行
        String markdown = buildMarkdown(doc.body(), opts).trim();

        // 7) 归一空白与符号
        markdown = normalize(markdown, opts);

        // 8) 长度预算裁剪
        if (opts.maxChars > 0 && markdown.length() > opts.maxChars) {
            markdown = truncateByBudget(markdown, opts.maxChars);
        }

        return markdown;
    }

    /**
     * 配置项
     */
    public static class CleanToMarkdownOptions {
        // 基础
        public boolean convertBoldToMarkdown = false;   // <b>/<strong> -> **text**
        public boolean convertItalicToMarkdown = false; // <i>/<em> -> *text*
        public boolean keepHeadingMarks = true;         // h1~h6 -> # 标题
        public boolean collapseBlankLines = true;       // 折叠多余空行

        // 图片处理
        public ImageMode imageMode = ImageMode.DATA_LATEX; // 图片摘要策略

        // 表格处理
        public int tableRowLimit = 20; // Markdown 表最大行数
        public int tableColLimit = 20;  // Markdown 表最大列数

        // 长度预算（字符数，非 tokens，仅做粗略控制）
        public int maxChars = 1400;          // 清洗后最大总长度（<=0 则不裁剪）
        public int maxFigureDescChars = 60;  // 单张图片摘要的最大长度
        public int maxCellChars = 60;        // 表格单元格最大字符

        // 其他
        public boolean normalizeSpaces = true;    // 归一空白符
        public boolean normalizeMathSymbols = true; // 归一部分数学符号（可按需扩充）
    }

    /** 图片处理模式 */
    public enum ImageMode {
        DROP,                       // 丢弃所有图片
        ALT_OR_TITLE,               // 使用 alt 或 title 摘要
        DATA_LATEX,                 // 仅保留 data-latex 公式
    }

    // ---------------- step 1: 危险/冗余节点清理 ----------------

    private static void removeDangerousNodes(Document doc) {
        // 移除明确无关/潜在风险标签
        doc.select("script, style, iframe, form, canvas, noscript, video, audio").remove();
        // 保留 <math>，但移除除 math 外的 svg
        doc.select("svg:not(math)").remove();
        // 有些广告/水印类
        doc.select("[class~=(?i)\\b(watermark|ad|banner|qr|barcode)\\b]").remove();
    }

    // ---------------- step 2: 属性清洗 ----------------

    private static void sanitizeAttributes(Document doc) {
        // 清空绝大多数元素属性，仅保留少数可读属性
        for (Element el : doc.getAllElements()) {
            String tag = el.tagName();
            if ("img".equals(tag)) {
                // 先取出感兴趣的属性
                String alt = el.attr("alt");
                String title = el.attr("title");
                String latex = el.attr("data-latex");
                // 清空再设回
                el.clearAttributes();
                if (!alt.isEmpty()) el.attr("alt", alt);
                if (!title.isEmpty()) el.attr("title", title);
                if (!latex.isEmpty()) el.attr("data-latex", latex);
            } else if ("math".equals(tag)) {
                // 保留 math 原文（不处理属性）
            } else {
                el.clearAttributes();
            }
        }
    }

    // ---------------- step 3: 上下标/MathML 转换 ----------------

    private static void convertSupSub(Document doc) {
        // <sup> -> ^{text}
        Elements sups = doc.select("sup");
        for (Element s : sups) {
            s.replaceWith(new TextNode("^{" + s.text() + "}"));
        }
        // <sub> -> _{text}
        Elements subs = doc.select("sub");
        for (Element s : subs) {
            s.replaceWith(new TextNode("_{" + s.text() + "}"));
        }
    }

    private static void convertMathMl(Document doc) {
        // 简化处理：将 <math> 内容作为 $...$ 包裹的文本占位
        Elements maths = doc.select("math");
        for (Element m : maths) {
            String mathText = m.outerHtml();
            // 若能接入 MathML->LaTeX 转换器，可在此替换
            if (StringUtils.isNotBlank(mathText)) {
                m.replaceWith(new TextNode(mathText));
            } else {
                m.remove();
            }
        }
    }

    // ---------------- step 4: 表格 -> Markdown ----------------

    private static int convertTablesToMarkdown(Document doc, CleanToMarkdownOptions opts) {
        int count = 0;
        for (Element table : doc.select("table")) {
            String md = tableToMarkdown(table, opts.tableRowLimit, opts.tableColLimit, opts.maxCellChars);
            table.replaceWith(new TextNode(md + "\n"));
            count++;
        }
        return count;
    }

    private static String tableToMarkdown(Element table, int rowLimit, int colLimit, int cellLimit) {
        StringBuilder md = new StringBuilder();
        Elements rows = table.select("tr");
        int r = 0;
        int headerCols;
        for (Element tr : rows) {
            if (r >= rowLimit) { md.append("…\n"); break; }
            Elements cells = tr.select("th,td");
            int cols = Math.min(cells.size(), colLimit);
            for (int c = 0; c < cols; c++) {
                String cell = truncate(cells.get(c).text().trim(), cellLimit);
                md.append("| ").append(cell.isEmpty() ? " " : cell).append(" ");
            }
            md.append("|\n");
            if (r == 0) {
                headerCols = cols;
                for (int c = 0; c < headerCols; c++) md.append("|---");
                md.append("|\n");
            }
            r++;
        }
        return md.toString();
    }

    // ---------------- step 5: 图片 -> 摘要占位 ----------------

    private static int convertImages(Document doc, CleanToMarkdownOptions opts) {
        int count = 0;
        for (Element img : doc.select("img")) {
            String replacement = null;
            String alt = img.attr("alt");
            String title = img.attr("title");
            String latex = img.attr("data-latex");

            switch (opts.imageMode) {
                case DROP: {
                    img.remove();
                    continue; // 不计入 count
                }
                case ALT_OR_TITLE: {
                    String desc = firstNonBlank(alt, title);
                    replacement = StringUtils.isBlank(desc) ? null : "[IMG] " + truncate(desc, opts.maxFigureDescChars);
                    break;
                }
                case DATA_LATEX: {
                    String desc = latexToDollar(latex);
                    replacement = StringUtils.isBlank(desc) ? null : "[IMG] " + truncate(desc, opts.maxFigureDescChars);
                    break;
                }
            }

            if (replacement != null) {
                img.replaceWith(new TextNode(replacement));
                count++;
            } else {
                // 没有可用描述：用占位标记
                img.replaceWith(new TextNode("[IMG]"));
                count++;
            }
        }
        return count;
    }

    private static String latexToDollar(String latex) {
        if (StringUtils.isBlank(latex)) return null;
        return "$" + latex.trim() + "$";
    }

    private static String firstNonBlank(String... vals) {
        for (String v : vals) if (StringUtils.isNotBlank(v)) return v;
        return null;
    }

    // ---------------- step 6: DOM -> Markdown 文本 ----------------

    private static String buildMarkdown(Node node, CleanToMarkdownOptions opts) {
        StringBuilder out = new StringBuilder();
        walk(node, out, opts);
        String s = out.toString();
        // 列表项过多前缀的空格 -> 统一
        s = s.replaceAll("[\t ]+\n", "\n");
        // 避免三个及以上连续空行
        if (opts.collapseBlankLines) s = s.replaceAll("\n{3,}", "\n\n");
        return s;
    }

    private static void walk(Node node, StringBuilder out, CleanToMarkdownOptions opts) {
        if (node instanceof TextNode) {
            out.append(((TextNode) node).text());
            return;
        }
        if (!(node instanceof Element)) {
            return;
        }
        Element el = (Element) node;
        String tag = el.tagName();
        switch (tag) {
            case "br":
                out.append("\n");
                break;
            case "p":
            case "div":
            case "section":
            case "article": {
                for (Node child : el.childNodes()) walk(child, out, opts);
                out.append("\n");
                break;
            }
            case "span":
            case "small":
            case "u": {
                for (Node child : el.childNodes()) walk(child, out, opts);
                break;
            }
            case "strong":
            case "b": {
                if (opts.convertBoldToMarkdown) out.append("**");
                for (Node child : el.childNodes()) walk(child, out, opts);
                if (opts.convertBoldToMarkdown) out.append("**");
                break;
            }
            case "em":
            case "i": {
                if (opts.convertItalicToMarkdown) out.append("*");
                for (Node child : el.childNodes()) walk(child, out, opts);
                if (opts.convertItalicToMarkdown) out.append("*");
                break;
            }
            case "h1":
            case "h2":
            case "h3":
            case "h4":
            case "h5":
            case "h6": {
                if (opts.keepHeadingMarks) {
                    int level = Integer.parseInt(tag.substring(1));
                    for (int j = 0; j < level; j++) out.append('#');
                    out.append(' ');
                }
                for (Node child : el.childNodes()) walk(child, out, opts);
                out.append("\n");
                break;
            }
            case "ul": {
                for (Element li : el.children()) {
                    if (!"li".equals(li.tagName())) continue;
                    out.append("- ");
                    for (Node child : li.childNodes()) walk(child, out, opts);
                    out.append("\n");
                }
                break;
            }
            case "ol": {
                int i = 1;
                for (Element li : el.children()) {
                    if (!"li".equals(li.tagName())) continue;
                    out.append(i++).append(". ");
                    for (Node child : li.childNodes()) walk(child, out, opts);
                    out.append("\n");
                }
                break;
            }
            case "li": {
                out.append("- ");
                for (Node child : el.childNodes()) walk(child, out, opts);
                out.append("\n");
                break;
            }
            default: {
                // 其他未知标签：当作行内容器
                for (Node child : el.childNodes()) walk(child, out, opts);
                break;
            }
        }
    }

    // ---------------- step 7: 归一化 ----------------

    private static String normalize(String s, CleanToMarkdownOptions opts) {
        if (opts.normalizeSpaces) {
            // 不间断空格 -> 普通空格
            s = s.replace('\u00A0', ' ');
            // Windows/Mac 行尾 -> \n
            s = s.replace("\r\n", "\n").replace("\r", "\n");
            // 去除每行首尾多余空白
            s = Arrays.stream(s.split("\n", -1))
                    .map(String::trim)
                    .collect(Collectors.joining("\n"));
            // 连续空格压缩（但保留公式中的空格，这里不做复杂解析）
            s = s.replaceAll("[ \t]{2,}", " ");
        }
        if (opts.normalizeMathSymbols) {
            s = normalizeMathSymbols(s);
        }
        return s.trim();
    }

    private static String normalizeMathSymbols(String s) {
        // 轻量归一：常见符号同形处理，可按需扩充
        s = s.replace('﹣', '−')   // 全角减号 -> 负号
                .replace('－', '−')
                .replace('–', '−')    // en dash -> 负号
                .replace('—', '−');   // em dash -> 负号
        // 乘除
        s = s.replace('＊', '×').replace('✖', '×').replace('／', '÷');
        // 小于等于/大于等于的变体
        s = s.replace("≦", "≤").replace("≧", "≥");
        // 全角空格 -> 半角
        s = s.replace('\u3000', ' ');
        return s;
    }

    // ---------------- step 8: 长度预算 ----------------

    private static String truncateByBudget(String s, int maxChars) {
        if (s.length() <= maxChars) return s;
        // 尝试按段落截断，避免切断句子
        String[] paras = s.split("\n\n");
        StringBuilder out = new StringBuilder(maxChars + 64);
        for (String p : paras) {
            if (out.length() + p.length() + 2 > maxChars) break;
            if (out.length() > 0) out.append("\n\n");
            out.append(p);
        }
        if (out.length() == 0) {
            // 极端情况：直接硬截
            return truncate(s, maxChars);
        }
        return out.toString();
    }

    private static String truncate(String s, int maxChars) {
        if (s == null) return null;
        if (maxChars <= 0 || s.length() <= maxChars) return s;
        return s.substring(0, Math.max(0, maxChars - 1)) + "…";
    }
}
