package com.sure.question.util;

import com.sure.question.enums.FileTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

public class MultipartFileUtil {

    public static boolean isPdf(MultipartFile file) {
        return FileTypeEnum.PDF.isFileType(file.getContentType());
    }

    public static boolean isWord(MultipartFile file) {
        return FileTypeEnum.WORD.isFileType(file.getContentType());
    }

    public static boolean isImage(MultipartFile file) {
        return FileTypeEnum.IMAGE.isFileType(file.getContentType());
    }

    public static boolean isAudio(MultipartFile file) {
        return FileTypeEnum.AUDIO.isFileType(file.getContentType());
    }

    /**
     * 获取文件名，不包含扩展名
     */
    public static String getName(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return StringUtils.EMPTY;
        }
        int idx = fileName.lastIndexOf(".");
        if (idx > 0) {
            return fileName.substring(0, idx);
        } else {
            return fileName;
        }
    }

    /**
     * 获取原始文件扩展名
     */
    public static String getOriginalExtension(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return StringUtils.EMPTY;
        }
        int idx = fileName.lastIndexOf(".");
        if (idx > 0) {
            return fileName.substring(idx);
        }
        return "";
    }

    /**
     * 获取类型对应的扩展名
     */
    public static String getFileTypeExtension(MultipartFile file) {
        for (FileTypeEnum fileType : FileTypeEnum.values()) {
            String extension = fileType.getExtension(file.getContentType());
            if (StringUtils.isNotEmpty(extension)) {
                return extension;
            }
        }
        return null;
    }
}
