package com.sure.question.util;

import com.sure.common.exception.ApiException;
import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.cos.COSDictionary;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class PdfUtil {
    public static List<String> pdfToImage(InputStream pdfFileStream, float dpi, int maxPage, Function<Integer, String> imagePathGetter) throws IOException {
        List<String> imgPathList = new ArrayList<>();
        byte[] bytes = IOUtils.toByteArray(pdfFileStream);
        try (PDDocument document = Loader.loadPDF(bytes)) {
            if (document.getNumberOfPages() > maxPage) {
                throw new ApiException("pdf文件最多支持" + maxPage + "页");
            }
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            for (int page = 0; page < document.getNumberOfPages(); page++) {
                BufferedImage image = pdfRenderer.renderImageWithDPI(page, dpi);
                String imagePath = imagePathGetter.apply(page);
                ImageIO.write(image, "png", new File(imagePath));
                imgPathList.add(imagePath);
            }
        }
        return imgPathList;
    }

    /**
     * 清除pdf文件的元数据，如标题、作者、备注等
     */
    public static byte[] removeMetaData(MultipartFile file) throws IOException {
        return removeMetaData(file.getInputStream());
    }

    public static byte[] removeMetaData(InputStream pdfFileStream) throws IOException {
        byte[] bytes = IOUtils.toByteArray(pdfFileStream);
        try (PDDocument document = Loader.loadPDF(bytes)) {
            // 清空 Info Dictionary
            COSDictionary infoDict = document.getDocumentInformation().getCOSObject();
            infoDict.clear();

            // 删除 XMP 元数据流
            document.getDocumentCatalog().getCOSObject().removeItem(COSName.METADATA);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            document.save(out);
            return out.toByteArray();
        }
    }

    public static Rectangle getPageSize(MultipartFile file, int pageIndex) throws IOException {
        return getPageSize(file.getInputStream(), pageIndex);
    }

    public static Rectangle getPageSize(InputStream pdfFileStream, int pageIndex) throws IOException {
        byte[] bytes = IOUtils.toByteArray(pdfFileStream);
        try (PDDocument document = Loader.loadPDF(bytes)) {
            if (pageIndex >= document.getNumberOfPages()) {
                throw new ApiException("该页不存在");
            }
            PDPage page = document.getPage(pageIndex);

            PDRectangle box = page.getCropBox();
            if (box == null || box.getWidth() == 0 || box.getHeight() == 0) {
                box = page.getMediaBox();
            }

            // UserUnit（巨大页面会用到；默认1.0）
            float userUnit = 1.0f;
            try { userUnit = page.getUserUnit(); } catch (Throwable ignore) {}

            double widthMm  = box.getWidth() * userUnit / 72.0 * 25.4;
            double heightMm = box.getHeight() * userUnit / 72.0 * 25.4;

            return new Rectangle(0, 0, (int) Math.round(widthMm), (int) Math.round(heightMm));
        }
    }

    public static int getPages(MultipartFile file) throws IOException {
        return getPages(file.getInputStream());
    }

    public static int getPages(InputStream pdfFileStream) throws IOException {
        byte[] bytes = IOUtils.toByteArray(pdfFileStream);
        try (PDDocument document = Loader.loadPDF(bytes)) {
            return document.getNumberOfPages();
        }
    }

    /**
     * 常见纸型（mm）
     */
    static class Paper {
        String name;
        double w;
        double h;
        Paper(String name, double w, double h) {
            this.name=name;
            this.w=w;
            this.h=h;
        }
    }
    private static final List<Paper> PAPERS = Stream.of(
            new Paper("A3", 420, 297),
            new Paper("A4", 210, 297),
            new Paper("8K", 370, 260),
            new Paper("16K", 185, 260)
    ).collect(Collectors.toList());

    /**
     * 获取最匹配的纸型，宽高误差不超过阈值
     */
    public static String getSizeString(double widthMM, double heightMM, double thresholdMM) {
        Paper p = null;
        double error = 0;
        for (Paper paper : PAPERS) {
            double thisError = Math.max(Math.abs(widthMM - paper.w), Math.abs(heightMM - paper.h));
            if (thisError <= thresholdMM && (p == null || thisError < error)) {
                p = paper;
                error = thisError;
            }
        }
        return p == null ? null : p.name;
    }
}
