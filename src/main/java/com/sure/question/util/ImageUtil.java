package com.sure.question.util;

import com.sure.common.exception.ApiException;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Iterator;

public class ImageUtil {
    public static byte[] toByteArray(BufferedImage img, String format) throws IOException {
        if (!StringUtils.equalsAny(format, "jpg", "png", "gif")) {
            throw new ApiException("指定图片格式错误");
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(img, format, os);
        os.flush();
        byte[] bytes = os.toByteArray();
        os.close();
        return bytes;
    }

    public static String toBase64DataUrl(BufferedImage img, String format) throws IOException {
        byte[] bytes = toByteArray(img, format);
        String base64String = Base64.getEncoder().encodeToString(bytes);
        String mime = StringUtils.equals(format, "jpg") ? "jpeg" : format;
        return "data:image/" + mime + ";base64," + base64String;
    }

    /**
     * 从元数据获取图片文件宽高
     *
     * @param inputStream 输入流。<b>注意：此方法执行完毕后将会关闭该输入流。</b>
     * @return 返回包含宽度和高度的 Dimension 对象
     * @throws IOException 无法读取或不是支持的图片格式
     */
    public static Dimension getImageDimensionFromMetaData(InputStream inputStream) throws IOException {
        try (ImageInputStream in = ImageIO.createImageInputStream(inputStream)) {
            if (in == null) {
                throw new IOException("无法创建图片输入流");
            }
            final Iterator<ImageReader> readers = ImageIO.getImageReaders(in);
            if (!readers.hasNext()) {
                throw new IOException("找不到合适的图片读取器，不支持的图片格式");
            }
            ImageReader reader = readers.next();
            try {
                reader.setInput(in);
                return new Dimension(reader.getWidth(0), reader.getHeight(0));
            } finally {
                reader.dispose();
            }
        }
    }
}
