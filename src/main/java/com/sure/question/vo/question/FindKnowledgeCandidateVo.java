package com.sure.question.vo.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FindKnowledgeCandidateVo implements Comparable<FindKnowledgeCandidateVo> {
    private Integer knowledgeId;
    private String knowledgeName;
    private Double score;
    private Integer support;

    public int compareTo(FindKnowledgeCandidateVo other) {
        int result = Double.compare(other.getScore(), this.getScore());
        if (result == 0) {
            result = Integer.compare(other.getSupport(), this.getSupport());
        }
        return result;
    }
}
