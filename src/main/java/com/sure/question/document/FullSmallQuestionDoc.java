package com.sure.question.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FullSmallQuestionDoc {

    /**
     * 小题ID
     */
    private Long small_question_id;

    /**
     * 题目ID
     */
    private Long question_id;

    /**
     * 学段
     */
    private Byte grade_level;

    /**
     * 学科ID
     */
    private Byte subject_id;

    /**
     * 题目类型ID
     */
    private Integer question_type_id;

    /**
     * 小题类型ID
     */
    private Short small_question_type_id;

    /**
     * 年份
     */
    private Short year;

    /**
     * 年级IDs
     */
    private List<Byte> grade_ids;

    /**
     * 难度
     */
    private Byte difficulty;

    /**
     * 得分率
     */
    private Float score_rate;

    /**
     * 使用次数
     */
    private Integer use_count;

    /**
     * 答题次数
     */
    private Integer answer_count;

    /**
     * 知识点IDs
     */
    private List<Long> knowledge_ids = new ArrayList<>();

    /**
     * 材料HTML内容
     */
    private String material_html;

    /**
     * 题干HTML内容
     */
    private String stem_html;

    /**
     * 选项HTML内容
     */
    private String options_html;

    /**
     * 答案HTML内容
     */
    private String answer_html;

    /**
     * 解析HTML内容
     */
    private String explanation_html;

    /**
     * 不包含答案解析的文本内容（用于搜索）
     */
    private String text_no_answer;

    /**
     * 包含所有内容的文本（用于搜索）
     */
    private String text_all;
}
