package com.sure.question.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum FileTypeEnum implements BaseEnum<String, String> {
    PDF("pdf", "PDF", new HashMap<String, String>() {{
            put("application/pdf", ".pdf");
        }}),
    WORD("word", "WORD", new HashMap<String, String>() {{
        put("application/msword", ".doc");
        put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", ".docx");
    }}),
    IMAGE("image", "图片", new HashMap<String, String>() {{
        put("image/jpeg", ".jpg");
        put("image/png", ".png");
    }}),
    AUDIO("audio", "音频", new HashMap<String, String>() {{
        put("audio/mpeg", ".mp3");
    }});

    private final String id;
    private final String name;
    private final Map<String, String> mimeTypeExtensionMap;

    /**
     * 是否该文件类型
     */
    public boolean isFileType(String contentType) {
        return mimeTypeExtensionMap.containsKey(StringUtils.lowerCase(contentType));
    }

    /**
     * 获取文件后缀名
     */
    public String getExtension(String contentType) {
        return mimeTypeExtensionMap.get(StringUtils.lowerCase(contentType));
    }
}
