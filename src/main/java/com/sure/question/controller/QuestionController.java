package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.question.annotation.CheckSign;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.*;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.ques.QuesByKnowVo;
import com.sure.question.vo.ques.UpdateQues;
import com.sure.question.vo.question.*;
import com.sure.question.vo.requestVo.intelligentVo.IntelligentRequest;
import com.sure.question.vo.requestVo.intelligentVo.WrongQuesTrain;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/question")
@Api(tags = "题目接口")
@Validated
@RequiredArgsConstructor
public class QuestionController {
    private final QuestionService questionService;
    private final QuestionBuildService questionBuildService;
    private final QuestionSearchService questionSearchService;
    private final QuestionKnowledgeService questionKnowledgeService;
    private final QuesLogService quesLogService;
    private final PermissionService permissionService;

    // TODO 待删除
    @Deprecated
    @GetMapping("byPid/{paperId}")
    @ApiOperation("根据试卷id查找题目")
    public Result getQuestionsVoByPaperId(@PathVariable String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        // TODO 检查家长端请求
        if (StringUtils.isNotEmpty(userVo.getSchoolId())) {
            permissionService.checkViewPaperContentPermission(paperId, userVo);
        }
        List<UserQuestionVo> questionVos = questionBuildService.selectPaperUserQuestionVos(paperId, userVo.getUserId());
        quesLogService.saveQuesLog(userVo, paperId, request, "view");
        return Result.ok(questionVos);
    }

    // TODO 待删除
    @Deprecated
    @GetMapping("renderedQuestionsByPid/{paperId}")
    @ApiOperation("根据试卷id查找已渲染的题目")
    public Result getRenderedQuestionsVoByPaperId(@PathVariable String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        // TODO 检查家长端请求
        if (StringUtils.isNotEmpty(userVo.getSchoolId())) {
            permissionService.checkViewPaperContentPermission(paperId, userVo);
        }
        List<QuestionVo> questionVos = questionBuildService.selectRenderedFormulaPaperQuestionVos(paperId);
        quesLogService.saveQuesLog(userVo, paperId, request, "view");
        return Result.ok(questionVos);
    }

    @GetMapping("byQid/{qid}")
    @ApiOperation("根据题目ID查题目")
    public Result byQid(@PathVariable String qid, HttpServletRequest request) {
        QuestionVo questionVo = questionBuildService.selectQuestionVo(qid);
        // TODO 只允许学科编辑员可以直接按Id获取题目
//        permissionService.checkEditorPermission(TokenUserUtil.getTokenUserVo(request), questionVo.getStage(), questionVo.getSubjectId());
        return Result.ok(questionVo);
    }

    @PutMapping("byQids")
    @ApiOperation("批量查题（内部调用）")
    @CheckSign
    public Result byResourceQids(@RequestBody @NotEmpty List<String> questionIds) {
        List<String> distinctQuestionIds = questionIds.stream().distinct().collect(Collectors.toList());
        return Result.ok(questionBuildService.selectQuestionVos(distinctQuestionIds));
    }

    @GetMapping("renderedList")
    @ApiOperation("批量查题（已渲染公式）（内部调用）")
    @CheckSign
    public Result getRenderedList(@RequestParam @NotEmpty List<String> questionIds) {
        List<String> distinctQuestionIds = questionIds.stream().distinct().collect(Collectors.toList());
        return Result.ok(questionBuildService.selectRenderedFormulaQuestionVos(distinctQuestionIds));
    }

    @ApiOperation("多条件,分页查询收藏题目")
    @GetMapping("fav")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面大小", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科id", dataType = "int"),
            @ApiImplicitParam(name = "stage", value = "学段id", dataType = "int"),
            @ApiImplicitParam(name = "difficulty", value = "难度id", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "试卷类型", dataType = "int")})
    public Result getFavorite(@NotNull @Min(1) Integer page, @NotNull @Min(1) Integer size,
                              @NotNull Integer subject, @NotNull Integer stage,
                              Integer difficulty, Integer type, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(questionService.getFavorite(userId, page, size, stage, subject, type, difficulty));
    }

    @ApiOperation("改变题目收藏状态")
    @PutMapping("fav/{id}")
    public Result changeFav(@PathVariable("id") Long id, HttpServletRequest request) {
        TokenUserVo tokenUserVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(questionService.changeFav(id, tokenUserVo.getUserId(), tokenUserVo.getSchoolId()));
    }


    @ApiOperation("获取一批雪花Id")
    @GetMapping("newIds")
    public Result generateIds(@RequestParam @NotNull @Min(1) @Max(1000) Integer size) {
        return Result.ok(questionService.generateIds(size));
    }


    @ApiOperation("题库管理员或编辑员修改题目")
    @PutMapping("adminSave")
    @AutoLog(value = "编辑员修改题目")
    @WithRoles({RoleEnum.SysEditor, RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result adminSaveNew(@RequestBody @NotNull QuestionVo questionVo, HttpServletRequest request) {
        return Result.ok(questionService.editorChangeQuestion(questionVo, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("管理员或编辑员修改题目知识点")
    @PutMapping("adminChangeKnowledge")
    @AutoLog(value = "编辑员修改题目知识点")
    @WithRoles({RoleEnum.SysEditor, RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result adminChangeKnowledge(@RequestBody @NotEmpty List<ChangeKnowledgeParam> changeKnowledgeParamList, HttpServletRequest request) {
        questionService.editorChangeQuestionsKnowledge(changeKnowledgeParamList, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @ApiOperation("管理员或编辑员修改题目章节")
    @PutMapping("adminChangeChapter")
    @AutoLog(value = "编辑员修改题目章节")
    @WithRoles({RoleEnum.SysEditor, RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result adminChangeChapter(@RequestBody @NotEmpty List<ChangeChapterParam> changeChapterParamList, HttpServletRequest request) {
        questionService.editorChangeQuestionsChapter(changeChapterParamList, TokenUserUtil.getUserId(request));
        return Result.ok();
    }


    @ApiOperation("修改题目的作答次数和得分率")
    @PostMapping("updateQues")
    @CheckSign
    public Result updateQues(@RequestBody List<UpdateQues> updateQuesList) {
        questionService.updateQues(updateQuesList);
        return Result.ok();
    }


    @ApiOperation("根据章节多条件查询题目")
    @GetMapping("byChapter")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "请求页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面数据量", dataType = "int"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string"),
            @ApiImplicitParam(name = "stage", value = "学段ID", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科ID", dataType = "int"),
            @ApiImplicitParam(name = "year", value = "试卷年份", dataType = "int"),
            @ApiImplicitParam(name = "bookId", value = "教材Id", dataType = "int"),
            @ApiImplicitParam(name = "chapter", value = "章节Id", dataType = "int"),
            @ApiImplicitParam(name = "difficulty", value = "难度ID", dataType = "int"),
            @ApiImplicitParam(name = "ques_type", value = "试题类型ID", dataType = "int"),
            @ApiImplicitParam(name = "source", value = "试题来源(试卷类型)", dataType = "int"),
            @ApiImplicitParam(name = "region", value = "地区", dataType = "long"),
            @ApiImplicitParam(name = "sort_by", value = "排序规则,暂时只支持 time ", dataType = "string"),
            @ApiImplicitParam(name = "isDesc", value = "是否降序，默认为true", dataType = "boolean")})
    public Result byChapter(@Valid FindQuestionPageParam param, HttpServletRequest request) {
        return Result.ok(questionSearchService.findByChapterOrKnowledge(param, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("根据知识点多条件查询题目")
    @GetMapping("byKnowledge")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "请求页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面数据量", dataType = "int"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string"),
            @ApiImplicitParam(name = "stage", value = "学段ID", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科ID", dataType = "int"),
            @ApiImplicitParam(name = "year", value = "试卷年份", dataType = "int"),
            @ApiImplicitParam(name = "knowledge", value = "知识点ID", dataType = "int"),
            @ApiImplicitParam(name = "difficulty", value = "难度ID", dataType = "int"),
            @ApiImplicitParam(name = "ques_type", value = "试题类型ID", dataType = "int"),
            @ApiImplicitParam(name = "source", value = "试题来源(试卷类型)", dataType = "int"),
            @ApiImplicitParam(name = "region", value = "地区", dataType = "long"),
            @ApiImplicitParam(name = "sort_by", value = "排序规则, 支持：useCount-组卷次数, answerCount", dataType = "string"),
            @ApiImplicitParam(name = "isDesc", value = "是否降序，默认为true", dataType = "boolean"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int")})
    public Result byKnowledge(@Valid FindQuestionPageParam param, HttpServletRequest request) {
        return Result.ok(questionSearchService.findByChapterOrKnowledge(param, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("添加变式题根据知识点多条件查询题目")
    @PostMapping("variants")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "请求页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面数据量", dataType = "int"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string"),
            @ApiImplicitParam(name = "stage", value = "学段ID", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科ID", dataType = "int"),
            @ApiImplicitParam(name = "year", value = "试卷年份", dataType = "int"),
            @ApiImplicitParam(name = "difficulty", value = "难度ID", dataType = "int"),
            @ApiImplicitParam(name = "ques_type", value = "试题类型ID", dataType = "int"),
            @ApiImplicitParam(name = "source", value = "试题来源(试卷类型)", dataType = "int"),
            @ApiImplicitParam(name = "region", value = "地区", dataType = "long"),
            @ApiImplicitParam(name = "ignorePaperId", value = "忽略题目的试卷ID", dataType = "long"),
            @ApiImplicitParam(name = "sort_by", value = "排序规则,暂时只支持 time ", dataType = "string"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int")})
    public Result findVariants(@Valid FindQuestionPageParam param, @RequestBody @Valid QuesByKnowVo vo, HttpServletRequest request) {
        param.setPaperIds(vo.getPaperIds());
        param.setKnowledgeIds(vo.getKnowIds());
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(questionSearchService.findByAddVariantsParam(param, userId));
    }


    @ApiOperation("指定题目Id查相似题")
    @GetMapping("similarQuestions")
    public Result getSimilarQuestionsById(@RequestParam @NotEmpty String questionId, @RequestParam @NotNull @Min(1) @Max(100) Integer size, HttpServletRequest request) {
        return Result.ok(questionSearchService.findSimilarQuestions(questionId, size, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("传入题目内容查相似题")
    @PostMapping("similarQuestions")
    public Result getSimilarQuestionsByContent(@RequestBody QuestionVo questionVo, @RequestParam @NotNull @Min(1) @Max(100) Integer size, HttpServletRequest request) {
        return Result.ok(questionSearchService.findSimilarQuestions(questionVo, size, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("指定题目Id查相似题知识点")
    @GetMapping("similarQuestionsKnowledge")
    public Result getSimilarQuestionsKnowledgeById(@RequestParam @NotEmpty String questionId, @RequestParam @NotNull @Min(1) @Max(100) Integer size) {
        return Result.ok(questionSearchService.findSimilarQuestionsKnowledge(questionId, size));
    }

    @ApiOperation("传入题目内容查相似题知识点")
    @PostMapping("similarQuestionsKnowledge")
    public Result getSimilarQuestionsKnowledgeByContent(@RequestBody QuestionVo questionVo, @RequestParam @NotNull @Min(1) @Max(100) Integer size) {
        return Result.ok(questionSearchService.findSimilarQuestionsKnowledge(questionVo, size));
    }

    @ApiOperation("传入题目查知识点")
    @PostMapping("findKnowledge")
    public Result findKnowledge(@RequestBody QuestionVo questionVo, @RequestParam(required = false, defaultValue = "5") @NotNull @Min(1) @Max(10) Integer size) {
        return Result.ok(questionKnowledgeService.findQuestionKnowledge(questionVo, size));
    }

    @ApiOperation("获取推荐知识点Token，先调用此接口获取令牌，再轮询调用recommendQuestionKnowledge获取结果")
    @PostMapping("recommendKnowledgeToken")
    public Result recommendKnowledgeToken(@RequestBody QuestionVo questionVo, HttpServletRequest request) {
        return Result.ok(questionKnowledgeService.recommendQuestionKnowledgeToken(questionVo, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("获取推荐知识点结果")
    @GetMapping("recommendKnowledge")
    public Result recommendKnowledge(@RequestParam String recommendKnowledgeToken) {
        return Result.ok(questionKnowledgeService.recommendQuestionKnowledgeResult(recommendKnowledgeToken));
    }


    @ApiOperation("智能组卷")
    @PostMapping("intelligent")
    public Result getQuesIntelligent(@RequestBody IntelligentRequest intelligent, HttpServletRequest request) {
        return Result.ok(questionSearchService.getIntelligentPaperQuestions(intelligent, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("学情组卷")
    @PostMapping("wrongQuesTrain")
    public Result wrongQuesTrain(@RequestBody @Valid WrongQuesTrain wrongQuesTrain, HttpServletRequest request) {
        wrongQuesTrain.normalize();
        return Result.ok(questionSearchService.wrongQuesTrain(wrongQuesTrain, TokenUserUtil.getUserId(request)));
    }
}
