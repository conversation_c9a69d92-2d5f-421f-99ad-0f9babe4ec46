package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.entity.IngestionQuestion;
import com.sure.question.service.*;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.ingestion.IngestionPaperVo;
import com.sure.question.vo.ingestion.StructureVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("coachBookIngestion")
@Api(tags = "教辅录入")
@Validated
@RequiredArgsConstructor
public class CoachBookIngestionController {
    private final CoachBookIngestionService coachBookIngestionService;
    private final CoachBookIngestionUploadService coachBookIngestionUploadService;
    private final CoachBookIngestionStructureService coachBookIngestionStructureService;
    private final IngestionPaperService ingestionPaperService;
    private final IngestionPageOcrService ingestionPageOcrService;
    private final IngestionQuestionService ingestionQuestionService;

    @ApiOperation("根据教辅Id获取录入项目信息")
    @GetMapping("byCoachBookId")
    public Result getIngestionByCoachBookId(@RequestParam @NotNull Integer coachBookId, HttpServletRequest request) {
        return Result.ok(coachBookIngestionService.getByCoachBookId(coachBookId, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("创建录入项目")
    @PostMapping("create")
    public Result create(@RequestParam @NotNull Integer coachBookId,
                         @RequestParam @NotNull String fileType,
                         HttpServletRequest request) {
        return Result.ok(coachBookIngestionService.create(coachBookId, fileType, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("修改上传文件类型")
    @PutMapping("changeFileType")
    public Result changeFileType(@RequestParam @NotNull Integer ingestionId,
                                 @RequestParam @NotNull String fileType,
                                 HttpServletRequest request) {
        coachBookIngestionUploadService.changeFileType(ingestionId, fileType, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("上传pdf")
    @PostMapping("uploadPdf")
    public Result uploadPdf(@RequestParam @NotNull Integer ingestionId,
                            @RequestPart @NotNull MultipartFile file,
                            @RequestParam(required = false) Boolean autoConvertImage,
                            HttpServletRequest request) {
        return Result.ok(coachBookIngestionUploadService.uploadPdf(ingestionId, file, Boolean.TRUE.equals(autoConvertImage), TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("将pdf转为图片")
    @PutMapping("convertPdfToImage")
    public Result convertPdfToImage(@RequestParam @NotNull Integer ingestionId, HttpServletRequest request) {
        coachBookIngestionUploadService.convertPdfToImage(ingestionId, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("获取上传图片批次号")
    @GetMapping("uploadImagesBatchNumber")
    public Result uploadImagesBatchNumber(@RequestParam @NotNull Integer ingestionId, HttpServletRequest request) {
        return Result.ok(coachBookIngestionUploadService.getUploadImagesBatchNumber(ingestionId, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("上传图片")
    @PostMapping("uploadImage")
    public Result uploadImage(@RequestParam @NotNull Integer ingestionId,
                               @RequestParam @NotBlank String batchNumber,
                               @RequestParam @NotNull @Min(0) Integer index,
                               @RequestPart @NotNull MultipartFile file,
                               HttpServletRequest request) {
        return Result.ok(coachBookIngestionUploadService.uploadImage(ingestionId, batchNumber, index, file, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("上传图片完成")
    @PutMapping("uploadImageComplete")
    public Result uploadImageComplete(@RequestParam @NotNull Integer ingestionId,
                                      @RequestParam @NotNull String batchNumber,
                                      HttpServletRequest request) {
        return Result.ok(coachBookIngestionUploadService.uploadImageComplete(ingestionId, batchNumber, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("调整已上传图片（修改顺序或删除部分）")
    @PutMapping("changeImages")
    public Result changeImages(@RequestParam @NotNull Integer ingestionId,
                               @RequestBody @NotEmpty List<String> filePath,
                               HttpServletRequest request) {
        coachBookIngestionUploadService.changeImages(ingestionId, filePath, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("修改页面分栏")
    @PutMapping("changePageColumns")
    public Result changePageColumns(@RequestParam @NotNull Integer ingestionId,
                                    @RequestBody @NotEmpty List<PageImage> pageImages,
                                    HttpServletRequest request) {
        coachBookIngestionUploadService.changePageColumns(ingestionId, pageImages, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("删除上传文件")
    @DeleteMapping("deleteUploadedFile")
    public Result deleteUploadedFile(@RequestParam @NotNull Integer ingestionId, HttpServletRequest request) {
        coachBookIngestionUploadService.deleteUploadedFile(ingestionId, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("查询结构")
    @GetMapping("structure")
    public Result getStructures(@RequestParam @NotNull Integer ingestionId, HttpServletRequest request) {
        return Result.ok(coachBookIngestionStructureService.getStructures(ingestionId, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("保存结构")
    @PutMapping("structure/save")
    public Result saveStructures(@RequestParam @NotNull Integer ingestionId,
                                 @RequestBody List<StructureVo> structures,
                                 HttpServletRequest request) {
        coachBookIngestionStructureService.saveStructures(ingestionId, structures, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("识别目录结构")
    @PutMapping("structure/recognizeTOC")
    public Result recognizeTOCStructure(@RequestParam @NotNull Integer ingestionId, HttpServletRequest request) {
        coachBookIngestionStructureService.recognizeTOCStructure(ingestionId, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("获取目录识别结果")
    @GetMapping("structure/recognizeTOCResult")
    public Result getRecognizeTOCResult(@RequestParam @NotNull Integer ingestionId, HttpServletRequest request) {
        return Result.ok(coachBookIngestionStructureService.getRecognizeTOCResult(ingestionId, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("获取试卷")
    @GetMapping("paperPage")
    public Result getPapers(@RequestParam @NotNull Integer ingestionId, HttpServletRequest request) {
        return Result.ok(ingestionPaperService.getPapers(ingestionId, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("添加试卷")
    @PostMapping("paperPage")
    public Result addPapers(@RequestParam @NotNull Integer ingestionId,
                            @RequestBody @NotEmpty List<IngestionPaperVo> paperPages,
                            HttpServletRequest request) {
        ingestionPaperService.addPapers(ingestionId, paperPages, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("删除试卷")
    @DeleteMapping("paperPage")
    public Result deletePapers(@RequestParam @NotNull Integer ingestionId,
                               @RequestBody @NotEmpty List<Long> paperIds,
                               HttpServletRequest request) {
        ingestionPaperService.deletePapers(ingestionId, paperIds, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("修改试卷")
    @PutMapping("paperPage")
    public Result changePaper(@RequestParam @NotNull Integer ingestionId,
                              @RequestBody @NotNull IngestionPaperVo paperPage,
                              HttpServletRequest request) {
        ingestionPaperService.changePaper(ingestionId, paperPage, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("确认试卷页面区域")
    @PutMapping("paperPage/confirm")
    public Result confirmPaperPageAreas(@RequestParam @NotNull Integer ingestionId,
                                       @RequestParam @NotNull Long paperId,
                                       HttpServletRequest request) {
        ingestionPaperService.confirmPaperPageAreas(ingestionId, paperId, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("获取整页识别结果")
    @PostMapping("ocr/pageResult")
    public Result getIngestionPageOcrResult(@RequestParam @NotNull Integer ingestionId,
                                            @RequestBody(required = false) List<String> pageImageUrls,
                                            HttpServletRequest request) {
        return Result.ok(ingestionPageOcrService.getIngestionPageOcrResultList(ingestionId, pageImageUrls, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("获取题目划分识别结果")
    @PostMapping("ocr/questionResult")
    public Result getIngestionQuestionOcrResult(@RequestParam @NotNull Integer ingestionId,
                                                @RequestBody(required = false) List<String> pageImageUrls,
                                                HttpServletRequest request) {
        return Result.ok(ingestionPageOcrService.getIngestionQuestionOcrResultList(ingestionId, pageImageUrls, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("识别整页")
    @PutMapping("ocr/startPageOcr")
    public Result startPageOcr(@RequestParam @NotNull Integer ingestionId,
                               @RequestBody @NotEmpty List<String> pageImageUrls,
                               HttpServletRequest request) {
        ingestionPageOcrService.startPageOcr(ingestionId, pageImageUrls, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("识别题目划分")
    @PutMapping("ocr/startQuestionOcr")
    public Result startQuestionOcr(@RequestParam @NotNull Integer ingestionId,
                                   @RequestBody @NotEmpty List<String> pageImageUrls,
                                   HttpServletRequest request) {
        ingestionPageOcrService.startQuestionOcr(ingestionId, pageImageUrls, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("获取题目")
    @GetMapping("questionArea/list")
    public Result getQuestions(@RequestParam @NotNull Integer ingestionId,
                               @RequestParam @NotNull Long paperId,
                               HttpServletRequest request) {
        return Result.ok(ingestionQuestionService.getQuestions(ingestionId, paperId, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("添加题目区域")
    @PostMapping("questionArea/add")
    public Result addQuestions(@RequestParam @NotNull Integer ingestionId,
                               @RequestParam @NotNull Long paperId,
                               @RequestBody @NotEmpty List<IngestionQuestion> questions,
                               HttpServletRequest request) {
        ingestionQuestionService.addQuestions(ingestionId, paperId, questions, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("修改题目区域")
    @PutMapping("questionArea/change")
    public Result changeQuestions(@RequestParam @NotNull Integer ingestionId,
                                  @RequestParam @NotNull Long paperId,
                                  @RequestBody @NotEmpty List<IngestionQuestion> questions,
                                  HttpServletRequest request) {
        ingestionQuestionService.changeQuestions(ingestionId, paperId, questions, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("删除题目区域")
    @DeleteMapping("questionArea/delete")
    public Result deleteQuestions(@RequestParam @NotNull Integer ingestionId,
                                  @RequestParam @NotNull Long paperId,
                                  @RequestBody @NotEmpty List<Long> questionIds,
                                  HttpServletRequest request) {
        ingestionQuestionService.deleteQuestions(ingestionId, paperId, questionIds, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }
}
