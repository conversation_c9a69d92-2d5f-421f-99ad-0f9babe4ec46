package com.sure.question.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.question.annotation.CheckSign;
import com.sure.question.annotation.WithRoles;
import com.sure.question.entity.CoachBook;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.CoachBookService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.SimpleVo;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.*;
import com.sure.question.vo.dataVo.StrStrVo;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("coachBook")
@Api(tags = "教辅")
@Validated
@RequiredArgsConstructor
public class CoachBookController {
    private final CoachBookService coachBookService;

    @GetMapping("page")
    @ApiOperation("分页查询")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = InfoVo.class, responseContainer = "List")})
    public Result getCoachBookPage(@Validated QueryVo queryVo, @NotNull Integer page, @NotNull Integer size) {
        return Result.ok(coachBookService.listPage(queryVo, new Page<>(page, size)));
    }

    @GetMapping("info")
    @ApiOperation("教辅信息")
    public Result getCoachBookInfo(@NotNull Integer coachBookId) {
        return Result.ok(coachBookService.getInfo(coachBookId));
    }

    @PostMapping("add")
    @ApiOperation("添加教辅")
    @AutoLog("添加教辅")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result addCoachBook(@Validated InfoVo infoVo, MultipartFile coverFile) {
        return Result.ok(coachBookService.addBook(infoVo, coverFile));
    }

    @PutMapping("update")
    @ApiOperation("更新信息")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result updateCoachBook(@Validated InfoVo infoVo, MultipartFile coverFile) {
        coachBookService.updateBook(infoVo, coverFile);
        return Result.ok();
    }

    @DeleteMapping("delete")
    @ApiOperation("删除教辅")
    @AutoLog("删除教辅")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result deleteCoachBook(@NotNull Integer coachBookId) {
        coachBookService.deleteBook(coachBookId);
        return Result.ok();
    }

    @GetMapping("papers")
    @ApiOperation("查包含试卷")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = PaperVo.class, responseContainer = "List")})
    public Result getPapersOfBook(@NotNull Integer coachBookId, Integer paperGroupId, Boolean onlyOpen, HttpServletRequest request) {
        if (onlyOpen == null) {
            onlyOpen = false;
        }
        return Result.ok(coachBookService.getPapersOfBook(coachBookId, paperGroupId, onlyOpen, TokenUserUtil.getTokenUserVo(request)));
    }

    @PostMapping("downloadPaperFeedbackSheets")
    @ApiOperation("下载教辅同步卷反馈卡")
    @AutoLog("下载教辅同步卷反馈卡")
    public void downloadPaperFeedbackSheets(@RequestParam Integer coachBookId, @RequestBody List<String> paperIds, HttpServletRequest request, HttpServletResponse response) throws Exception {
        coachBookService.downloadPaperFeedbackSheets(coachBookId, paperIds, TokenUserUtil.getTokenUserVo(request), response.getOutputStream());
    }

    @PostMapping("downloadCoachBookFeedbackSheets")
    @ApiOperation("下载教辅同步卷反馈卡，含未关联试卷")
    @AutoLog("下载教辅同步卷反馈卡，含未关联试卷")
    public void downloadCoachBookFeedbackSheets(@RequestParam Integer coachBookId, @RequestBody List<String> paperIds, HttpServletRequest request, HttpServletResponse response) throws Exception {
        coachBookService.downloadCoachBookFeedbackSheets(coachBookId, paperIds, TokenUserUtil.getTokenUserVo(request), response.getOutputStream());
    }

    @PostMapping("downloadCoachBookPaperContentPdfZip")
    @ApiOperation("下载教辅试卷正文pdf压缩包")
    @AutoLog("下载教辅试卷正文pdf压缩包")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public void downloadCoachBookPaperContentPdfZip(@RequestParam Integer coachBookId, @RequestBody List<String> paperIds,
                                                    HttpServletRequest request, HttpServletResponse response) throws Exception {
        coachBookService.downloadCoachBookPaperAssetZip(coachBookId, paperIds, response.getOutputStream(), "content", TokenUserUtil.getTokenUserVo(request));
    }

    @PostMapping("downloadCoachBookPaperAnswerPdfZip")
    @ApiOperation("下载教辅试卷答案pdf压缩包")
    @AutoLog("下载教辅试卷答案pdf压缩包")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public void downloadCoachBookPaperAnswerPdfZip(@RequestParam Integer coachBookId, @RequestBody List<String> paperIds,
                                                   HttpServletRequest request, HttpServletResponse response) throws Exception {
        coachBookService.downloadCoachBookPaperAssetZip(coachBookId, paperIds, response.getOutputStream(), "answer", TokenUserUtil.getTokenUserVo(request));
    }

    @PostMapping("downloadCoachBookPaperListeningAudioZip")
    @ApiOperation("下载教辅试卷听力语音压缩包")
    @AutoLog("下载教辅试卷听力语音压缩包")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public void downloadCoachBookPaperListeningAudioZip(@RequestParam Integer coachBookId, @RequestBody List<String> paperIds,
                                                        HttpServletRequest request, HttpServletResponse response) throws Exception {
        coachBookService.downloadCoachBookPaperAssetZip(coachBookId, paperIds, response.getOutputStream(), "listening", TokenUserUtil.getTokenUserVo(request));
    }

    @GetMapping("customPapers")
    @ApiOperation("查包含定练习卷")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = PaperVo.class, responseContainer = "List")})
    public Result getCustomPapersOfBook(@NotNull Integer coachBookId, Integer paperGroupId, HttpServletRequest request) {
        return Result.ok(coachBookService.getCustomPapersOfBook(coachBookId, paperGroupId, TokenUserUtil.getTokenUserVo(request)));
    }

    @PostMapping("addPapers")
    @ApiOperation("添加同步卷到教辅")
    @AutoLog("添加同步卷到教辅")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result addPapersToBook(@NotNull Integer coachBookId, @RequestParam @NotEmpty List<String> paperIds, HttpServletRequest req) {
        coachBookService.addPapersToBook(coachBookId, paperIds, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @PostMapping("addCustomPapers")
    @ApiOperation("添加练习卷到教辅")
    @AutoLog("添加练习卷到教辅")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result addCustomPapersToBook(@NotNull Integer coachBookId, @RequestParam @NotEmpty List<String> paperIds, HttpServletRequest req) {
        coachBookService.addCustomPapersToBook(coachBookId, paperIds, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @PutMapping("sortPapers")
    @ApiOperation("调整试卷顺序")
    @AutoLog("调整教辅试卷顺序")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result updateBookPaperSortCode(@RequestBody @NotEmpty @Validated List<PaperVo> paperVos, HttpServletRequest req) {
        coachBookService.updateBookPaperSortCode(paperVos, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @PutMapping("sortCustomPapers")
    @ApiOperation("调整定制试卷顺序")
    @AutoLog("调整教辅定制试卷顺序")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result updateBookCustomPaperSortCode(@RequestBody @NotEmpty @Validated List<PaperVo> paperVos, HttpServletRequest req) {
        coachBookService.updateBookCustomPaperSortCode(paperVos, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @DeleteMapping("removePapers")
    @ApiOperation("移除试卷")
    @AutoLog("教辅移除试卷")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result removePapersFromBook(@NotNull Integer coachBookId, @RequestParam @NotEmpty List<String> paperIds, HttpServletRequest req) {
        coachBookService.removePapersFromBook(coachBookId, paperIds, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @DeleteMapping("removeCustomPapers")
    @ApiOperation("移除定制试卷")
    @AutoLog("教辅移除定制试卷")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result removeCustomPapersFromBook(@NotNull Integer coachBookId, @RequestParam @NotEmpty List<String> paperIds, HttpServletRequest req) {
        coachBookService.removeCustomPapersFromBook(coachBookId, paperIds, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @PutMapping("updatePaperAlias")
    @ApiOperation("修改教辅试卷别名")
    @AutoLog("修改教辅试卷别名")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result updatePaperAlias(@NotNull Integer coachBookId, @RequestBody @NotEmpty List<StrStrVo> paperIdAliasList, HttpServletRequest req) {
        coachBookService.updateBookPaperAlias(coachBookId, paperIdAliasList, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @PutMapping("updateScoreAnalysis")
    @ApiOperation("修改是否启用分值分析")
    @AutoLog("修改是否启用分值分析")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result updateScoreAnalysis(@NotNull Integer coachBookId, @RequestBody @NotEmpty List<StrStrVo> paperIdScoreAnalysisList, HttpServletRequest req) {
        coachBookService.updateBookPaperScoreAnalysis(coachBookId, paperIdScoreAnalysisList, TokenUserUtil.getTokenUserVo(req));
        return Result.ok();
    }

    @ApiOperation("获取排序后的试卷ID列表")
    @PostMapping("orderPaperIds")
    public Result getOrderPaperIds(@RequestBody @NotEmpty @Validated List<String> paperIds) {
        List<String> orderPaperIds = coachBookService.getOrderPaperIds(paperIds);
        return Result.ok(orderPaperIds);
    }

    @ApiOperation("设置教辅单元卷开放显示时间")
    @PostMapping("setPaperOpenTime")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result setPaperOpenTime(@RequestBody @NotEmpty List<OpenTimeVo> vos) {
        coachBookService.setPaperOpenTime(vos);
        return Result.ok();
    }

    @ApiOperation("设置教辅定制试卷开放显示时间")
    @PostMapping("setCustomPaperOpenTime")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result setCustomPaperOpenTime(@RequestBody @NotEmpty List<OpenTimeVo> vos) {
        coachBookService.setCustomPaperOpenTime(vos);
        return Result.ok();
    }

    @ApiOperation("设置教辅单元卷是否锁定")
    @PutMapping("setPaperLocked")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result setPaperLocked(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotEmpty String paperId, @RequestParam @NotNull Boolean locked) {
        coachBookService.setPaperLocked(coachBookId, paperId, locked);
        return Result.ok();
    }

    @ApiOperation("上传教辅单元卷正文pdf")
    @PutMapping("uploadPaperContentPdf")
    @AutoLog("上传教辅单元卷正文pdf")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result uploadPaperContentPdf(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotEmpty String paperId,
                                        @RequestBody @NotNull MultipartFile file, HttpServletRequest request) {
        return Result.ok(coachBookService.uploadPaperAsset(coachBookId, paperId, file, "content", TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("上传教辅单元卷答案pdf")
    @PutMapping("uploadPaperAnswerPdf")
    @AutoLog("上传教辅单元卷答案pdf")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result uploadPaperAnswerPdf(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotEmpty String paperId,
                                       @RequestBody @NotNull MultipartFile file, HttpServletRequest request) {
        return Result.ok(coachBookService.uploadPaperAsset(coachBookId, paperId, file, "answer", TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("上传教辅单元卷听力语音")
    @PutMapping("uploadPaperListeningAudio")
    @AutoLog("上传教辅单元卷听力语音")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result uploadPaperListeningAudio(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotEmpty String paperId,
                                            @RequestBody @NotNull MultipartFile file, HttpServletRequest request) {
        return Result.ok(coachBookService.uploadPaperAsset(coachBookId, paperId, file, "listening", TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("删除教辅单元卷正文pdf")
    @DeleteMapping("deletePaperContentPdf")
    @AutoLog("删除教辅单元卷正文pdf")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result deletePaperContentPdf(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotEmpty String paperId) {
        coachBookService.deletePaperAsset(coachBookId, paperId, "content");
        return Result.ok();
    }

    @ApiOperation("删除教辅单元卷答案pdf")
    @DeleteMapping("deletePaperAnswerPdf")
    @AutoLog("删除教辅单元卷答案pdf")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result deletePaperAnswerPdf(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotEmpty String paperId) {
        coachBookService.deletePaperAsset(coachBookId, paperId, "answer");
        return Result.ok();
    }

    @ApiOperation("删除教辅单元卷听力语音")
    @DeleteMapping("deletePaperListeningAudio")
    @AutoLog("删除教辅单元卷听力语音")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result deletePaperListeningAudio(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotEmpty String paperId) {
        coachBookService.deletePaperAsset(coachBookId, paperId, "listening");
        return Result.ok();
    }

    @ApiOperation("获取定制卷PDF")
    @GetMapping("getCustomPaperPdf")
    public Result getCustomPaperPdf(@RequestParam @NotNull Integer coachBookId, @RequestParam @NotBlank String paperId, String studentId, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        PdfVo vo = coachBookService.getCustomPaperPdf(coachBookId, paperId, studentId, userInfo.getUserId());
        return Result.ok(vo);
    }

    @ApiOperation("获取学生未读试卷数量")
    @GetMapping("getUnreadPaperCount")
    public Result getUnreadPaperCount(@RequestParam @NotBlank String studentId, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        Integer count = coachBookService.getUnreadPaperCount(studentId, userInfo);
        return Result.ok(count);
    }

    @GetMapping("sheets")
    @ApiOperation("查包含反馈卡")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = SheetVo.class, responseContainer = "List")})
    public Result getSheetsOfBook(@NotNull Integer coachBookId, HttpServletRequest request) {
        return Result.ok(coachBookService.getSheetsOfBook(coachBookId, TokenUserUtil.getTokenUserVo(request)));
    }

    @PostMapping("addSheets")
    @ApiOperation("添加反馈卡（内部调用）")
    @AutoLog("添加反馈卡到教辅")
    public Result addSheetsToBook(@NotNull Integer coachBookId, @RequestParam @NotEmpty List<String> sheetIds, String userId) {
        coachBookService.addSheetsToBook(coachBookId, sheetIds, userId);
        return Result.ok();
    }

    @PutMapping("sortSheets")
    @ApiOperation("调整反馈卡顺序")
    @AutoLog("调整教辅反馈卡顺序")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result updateBookSheetSortCode(@RequestBody @NotEmpty @Validated List<SheetVo> sheetVos) {
        coachBookService.updateBookSheetSortCode(sheetVos);
        return Result.ok();
    }

    @DeleteMapping("removeSheets")
    @ApiOperation("移除反馈卡")
    @AutoLog("教辅移除反馈卡")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result removeSheetsFromBook(@NotNull Integer coachBookId, @RequestParam @NotEmpty List<String> sheetIds) {
        coachBookService.removeSheetsFromBook(coachBookId, sheetIds);
        return Result.ok();
    }

    @PutMapping("linkSheetToPaper")
    @ApiOperation("关联反馈卡到试卷")
    @AutoLog("关联反馈卡到试卷")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result linkSheetToPaper(@NotNull Integer coachBookId, @NotEmpty String sheetId, @NotEmpty String paperId) {
        coachBookService.linkSheetToPaper(coachBookId, sheetId, paperId);
        return Result.ok();
    }

    @ApiOperation("查询同步卷对应的教辅名称")
    @PostMapping("getBookNameBySyncPaperIds")
    public Result getBookNameBySyncPaperIds(@RequestBody @NotEmpty List<String> paperIds) {
        List<PaperCoachVo> voList = coachBookService.getBookNameBySyncPaperIds(paperIds);
        return Result.ok(voList);
    }

    @ApiOperation("查询特定学期的全部教辅信息")
    @GetMapping("listCoachBook")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CoachBook.class, responseContainer = "List")})
    public Result listCoachBook(Integer semesterId, Integer term) {
        List<CoachBook> list = coachBookService.listCoachBook(semesterId, term);
        return Result.ok(list);
    }

    @ApiOperation("查询特定学期开通教辅的全部学校列表（学校ID、名称）")
    @GetMapping("listSchools")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = SimpleVo.class, responseContainer = "List")})
    public Result listSchools(Integer semesterId, Integer term) {
        List<SimpleVo> list = coachBookService.listSchools(semesterId, term);
        return Result.ok(list);
    }

    @ApiOperation("机构查询特定学期开通教辅的全部学校列表（学校ID、名称）")
    @GetMapping("listSchoolsByOrg")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = SimpleVo.class, responseContainer = "List")})
    public Result listSchoolsByOrg(Integer semesterId, Integer term, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        List<SimpleVo> list = coachBookService.listSchoolsByOrg(semesterId, term, userInfo);
        return Result.ok(list);
    }

    @ApiOperation("设置教辅错题标记方式")
    @PostMapping("setWrongQuesMark")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result setWrongQuesMark(@RequestBody @Valid List<WrongQuesMarkVo> vos) {
        coachBookService.setWrongQuesMark(vos);
        return Result.ok();
    }

    @ApiOperation("查询特定学年学期任教年级科目教辅信息")
    @GetMapping("teachingGradeSubjectCoachBooks")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CoachBook.class, responseContainer = "List")})
    public Result teachingGradeSubjectCoachBooks(Integer semesterId, Integer term, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        List<CoachBook> list = coachBookService.teachingGradeSubjectCoachBooks(semesterId, term, userInfo);
        return Result.ok(list);
    }

    @ApiOperation("查询指定学年学期教辅班级的试卷列表")
    @GetMapping("paperExams")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookId", value = "教辅ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "classId", value = "班级ID", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "isAsc", value = "是否升序", dataType = "boolean", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = PaperExamInfoVo.class, responseContainer = "List")})
    public Result paperExams(@RequestParam Integer semesterId, @RequestParam Integer term, @RequestParam Integer coachBookId,
                             @RequestParam String classId, @RequestParam Boolean isAsc, HttpServletRequest request) {
        if (isAsc == null) {
            isAsc = true;
        }
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        List<PaperExamInfoVo> voList = coachBookService.paperExams(semesterId, term, coachBookId, classId, isAsc, userInfo);
        return Result.ok(voList);
    }

    @ApiOperation("查询试卷所属教辅试卷信息列表")
    @PutMapping("paperCoachBookInfos")
    public Result paperCoachBookInfos(@RequestBody Set<String> paperIds) {
        List<BookPaperVo> voList = coachBookService.paperCoachBookInfos(paperIds);
        return Result.ok(voList);
    }

    @ApiOperation("查询教辅信息（按答题卡ID）")
    @GetMapping("getCoachBookBySheetId")
    public Result getCoachBookBySheetId(@RequestParam String schoolId, @RequestParam String userId, @RequestParam String sheetId) {
        return Result.ok(coachBookService.getSchoolCoachBookBySheetId(schoolId, userId, sheetId));
    }

    @ApiOperation("查询教辅信息（按试卷ID）")
    @GetMapping("getCoachBookByPaperId")
    public Result getCoachBookByPaperId(@RequestParam String schoolId, @RequestParam String userId, @RequestParam String paperId) {
        return Result.ok(coachBookService.getSchoolCoachBookByPaperId(schoolId, userId, paperId));
    }

    @ApiOperation("查询教辅中试卷的ID列表")
    @GetMapping("listCoachBookPaperIds")
    public Result listCoachBookPaperIds(Integer coachBookId) {
        List<Long> paperIds = coachBookService.listCoachBookPaperIds(coachBookId);
        return Result.ok(paperIds);
    }

    @ApiOperation("查询教辅试卷是否启用分值分析")
    @GetMapping("scoreAnalysisStatus")
    public Result getScoreAnalysisStatus(Integer coachBookId, Long paperId) {
        Boolean status = coachBookService.getScoreAnalysisStatus(coachBookId, paperId);
        return Result.ok(status);
    }

    @ApiOperation("查询教辅名称")
    @PutMapping("getCoachBookNames")
    public Result getCoachBookNames(@RequestBody List<Integer> coachBookIds) {
        List<SimpleVo> voList = coachBookService.getCoachBookNames(coachBookIds);
        return Result.ok(voList);
    }

    @ApiOperation("查询教辅错题套餐")
    @GetMapping("schoolCoachBookPlans")
    public Result getSchoolCoachBookPlans(String schoolId, Integer gradeId, Integer semesterId, Integer term) {
        List<CoachBookPlanVo> voList = coachBookService.getSchoolCoachBookPlans(schoolId, gradeId, semesterId, term);
        return Result.ok(voList);
    }

    @ApiOperation("查试卷是否在教辅中及是否已锁定")
    @GetMapping("coachBookPaperInfo")
    @CheckSign
    public Result getCoachBookPaperInfoByPaperId(String paperId) {
        return Result.ok(coachBookService.getCoachBookPaperInfoByPaperId(paperId));
    }

    @ApiOperation("更新教辅试卷答题卡信息")
    @PutMapping("updatePaperSheetInfo")
    @CheckSign
    public Result updatePaperSheetInfo(@RequestParam Integer coachBookId, @RequestParam String paperId, @RequestParam String userId,
                                       @RequestBody(required = false) SheetVo sheet) {
        coachBookService.updatePaperSheetInfo(coachBookId, paperId, userId, sheet);
        return Result.ok();
    }
}
