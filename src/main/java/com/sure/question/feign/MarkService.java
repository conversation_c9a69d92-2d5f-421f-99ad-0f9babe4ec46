package com.sure.question.feign;

import com.sure.common.entity.Result;
import com.sure.question.vo.mark.ExamStatisticsVo;
import com.sure.question.vo.schoolCoachBook.PaperWrongCountReqVo;
import com.sure.question.vo.schoolCoachBook.PaperWrongCountVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "SURE-MARK")
public interface MarkService {
    /**
     * 获取一批试卷对应的错题反馈卡
     */
    @GetMapping("feedbackSheet/paperIdSheetMap")
    Result getPaperIdFeedbackSheetMap(@RequestParam String paperIds);

    /**
     * 获取一批试卷对应的答题卡
     */
    @GetMapping("answerSheet/paperIdSheetMap")
    Result getPaperIdAnswerSheetMap(@RequestParam String paperIds);

    /**
     * 获取一批反馈卡信息
     */
    @GetMapping("feedbackSheet/sheetsInfo")
    Result getFeedbackSheetsInfo(@RequestParam String sheetIds);

    /**
     * 获取一批答题卡信息
     */
    @GetMapping("answerSheet/sheetsInfo")
    Result getAnswerSheetsInfo(@RequestParam String sheetIds);

    /**
     * 按试卷Id获取一批答题卡信息
     */
    @GetMapping("answerSheet/sheetsInfoByPaperIds")
    Result getAnswerSheetsInfoByPaperIds(@RequestParam String paperIds);

    /**
     * 清理试卷对应的错题反馈卡
     */
    @PutMapping("feedbackSheet/clean")
    Result cleanFeedbackSheet(@RequestParam String paperId);

    /**
     * 检查试卷有没有绑定考试科目
     */
    @GetMapping("subject/paper/checkPaperExamSubjectBinding")
    Result checkPaperExamSubjectBinding(@RequestParam String paperId);

    /**
     * 查询考试科目列表（按考试科目 ids）
     */
    @PostMapping("subject/listExamSubjectByIds")
    Result listExamSubjectByIds(@RequestBody List<String> examSubjectIds);

    /**
     * 查询学生试卷做错的题目数量
     *
     * @param vo paperIds, studentId
     * @return List<PaperWrongCountVo>
     */
    @PostMapping("feedback/listWrongQuesCount")
    List<PaperWrongCountVo> listPaperWrongCount(@RequestBody PaperWrongCountReqVo vo);

    /**
     * 将反馈卡来源改为由试卷创建
     */
    @PutMapping("feedbackSheet/changeSheetSourceToPaper")
    Result changeFeedbackSheetSourceToPaper(@RequestParam String sheetId, @RequestParam String paperId);

    /**
     * 将答题卡来源改为由试卷创建
     */
    @PutMapping("answerSheet/changeSheetSourceToPaper")
    Result changeAnswerSheetSourceToPaper(@RequestParam String sheetId, @RequestParam String paperId);

    @PostMapping("feedback/examStatistics")
    Result examStatistics(@RequestBody ExamStatisticsVo vo);

}
