package com.sure.question.jobHandler;

import com.sure.question.entity.QuestionEsSync;
import com.sure.question.mapper.QuestionEsSyncMapper;
import com.sure.question.service.QuestionDocService;
import com.sure.question.vo.dataVo.StrStrVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XxlJobHandler {
    @Resource
    private QuestionEsSyncMapper questionEsSyncMapper;
    @Resource
    private QuestionDocService questionDocService;
    /**
     * （第二版）同步题目到 es 库
     */
    @XxlJob("syncQuesV2")
    public ReturnT<String> syncQuesV2(String param) {
        XxlJobLogger.log("【同步题目V2】开始");

        // 本次任务处理的所有记录及结果
        Map<Long, Boolean> taskResultMap = new HashMap<>();
        long beginTick = System.currentTimeMillis();

        try {
            // 将同步中断（执行超过1小时）的题目设为待同步
            questionEsSyncMapper.updateTimeoutToPending(60);

            int batchNo = 0;
            while (true) {
                long batchBeginTick = System.currentTimeMillis();

                // 获取待同步的一批记录
                List<QuestionEsSync> batch = questionEsSyncMapper.selectNeedSyncBatch(50);
                if (batch.isEmpty()) {
                    break;
                }

                // 设置状态为同步中
                List<Long> batchIds = batch.stream().map(QuestionEsSync::getId).collect(Collectors.toList());
                questionEsSyncMapper.updateSyncInProgressBatch(batchIds);

                // 按题目分组
                Map<String, List<Long>> groupByQuestionId = batch.stream().collect(Collectors.groupingBy(x -> x.getQuestionId().toString(),
                        Collectors.mapping(QuestionEsSync::getId, Collectors.toList())));
                int questionCount = groupByQuestionId.size();

                // 执行同步
                Map<String, String> failMap = questionDocService.sync(groupByQuestionId.keySet());

                // 保存失败记录
                List<StrStrVo> failedList = new ArrayList<>();
                failMap.forEach((questionId, reason) -> {
                    String shortReason = StringUtils.substring(reason, 0, 10000);
                    List<Long> idList = groupByQuestionId.get(questionId);
                    if (idList != null) {
                        idList.forEach(id -> {
                            failedList.add(new StrStrVo(id.toString(), shortReason));
                            taskResultMap.put(id, false);
                        });
                    }
                    groupByQuestionId.remove(questionId);
                });
                if (!failedList.isEmpty()) {
                    questionEsSyncMapper.updateSyncFailedBatch(failedList);
                }

                // 保存成功记录
                List<Long> succeededList = new ArrayList<>();
                groupByQuestionId.forEach((questionId, idList) -> {
                    succeededList.addAll(idList);
                    idList.forEach(id -> taskResultMap.put(id, true));
                });
                if (!succeededList.isEmpty()) {
                    questionEsSyncMapper.updateSyncSucceededBatch(succeededList);
                }

                XxlJobLogger.log("【同步题目V2】执行中，第 {} 批，{} 条记录，{} 道题目，成功 {} 条，失败 {} 条，耗时 {} ms",
                        ++batchNo, batch.size(), questionCount, succeededList.size(), failMap.size(), System.currentTimeMillis() - batchBeginTick);
            }
        } catch (Exception ex) {
            XxlJobLogger.log("【同步题目V2】出错，耗时 {} ms，异常信息：\n{}",
                    System.currentTimeMillis() - beginTick, ExceptionUtils.getStackTrace(ex));
            return ReturnT.FAIL;
        }

        long failedCount = taskResultMap.values().stream().filter(x -> !x).count();
        if (failedCount == 0) {
            XxlJobLogger.log("【同步题目V2】成功，共处理 {} 条记录，耗时 {} ms",
                    taskResultMap.size(), System.currentTimeMillis() - beginTick);
            return ReturnT.SUCCESS;
        } else {
            XxlJobLogger.log("【同步题目V2】完成，共处理 {} 条记录，成功 {} 条，失败 {} 条，耗时 {} ms",
                    taskResultMap.size(), taskResultMap.size() - failedCount, failedCount, System.currentTimeMillis() - beginTick);
            return ReturnT.FAIL;
        }
    }
}
