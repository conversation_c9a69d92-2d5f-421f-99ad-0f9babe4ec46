<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.IngestionPageOcrMapper">
    <update id="updatePageOcrStatusToWaiting">
        UPDATE ingestion_page_ocr
        SET page_ocr_status = 'W'
        WHERE ingestion_id = #{ingestionId}
        <if test="pageImageUrls != null and !pageImageUrls.isEmpty()">
            AND page_image_url IN <foreach item="item" collection="pageImageUrls" open="(" separator="," close=")">#{item}</foreach>
        </if>
        AND (page_ocr_status IN ('I', 'F')
        OR (page_ocr_status = 'R' AND page_ocr_start_time &lt; DATE_SUB(NOW(), INTERVAL 1 MINUTE))
        )
    </update>

    <update id="updatePageOcrStatusToRunning">
        UPDATE ingestion_page_ocr
        SET page_ocr_status = 'R', page_ocr_start_time = NOW()
        WHERE ingestion_id = #{ingestionId}
        AND page_image_url = #{pageImageUrl}
        AND page_ocr_status = 'W'
    </update>

    <update id="updateQuestionOcrStatusToWaiting">
        UPDATE ingestion_page_ocr
        SET question_ocr_status = 'W'
        WHERE ingestion_id = #{ingestionId}
        <if test="pageImageUrls != null and !pageImageUrls.isEmpty()">
            AND page_image_url IN <foreach item="item" collection="pageImageUrls" open="(" separator="," close=")">#{item}</foreach>
        </if>
        AND (question_ocr_status IN ('I', 'F')
            OR (question_ocr_status = 'R' AND question_ocr_start_time &lt; DATE_SUB(NOW(), INTERVAL 1 MINUTE))
        )
    </update>

    <update id="updateQuestionOcrStatusToRunning">
        UPDATE ingestion_page_ocr
        SET question_ocr_status = 'R', question_ocr_start_time = NOW()
        WHERE ingestion_id = #{ingestionId}
          AND page_image_url = #{pageImageUrl}
          AND question_ocr_status = 'W'
    </update>
</mapper>
